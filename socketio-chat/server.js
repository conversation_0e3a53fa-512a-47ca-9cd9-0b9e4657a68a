const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const multer = require('multer');
const path = require('path');

const app = express();
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: function (req, file, cb) {
        // Allow images and common file types
        const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt|mp3|mp4|avi/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Only specific file types are allowed!'));
        }
    }
});

// Serve uploaded files statically
app.use('/uploads', express.static('uploads'));

const server = http.createServer(app);

const io = new Server(server, {
    cors: {
        origin: 'http://localhost:3000', // React client URL
        methods: ['GET', 'POST'],
    },
});

// Track users and rooms
const onlineUsers = {};
const userRooms = {};
// Track message reactions and read receipts
const messageReactions = {};
const messageReadReceipts = {};
// Store message history for pagination
const messageHistory = [];
let messageIdCounter = 0;

io.on('connection', (socket) => {
    console.log(`User connected: ${socket.id}`);

    socket.on('set_username', (username) => {
        socket.username = username;
        onlineUsers[socket.id] = username;
        console.log(`Username set: ${username}`);
        io.emit('online_users', Object.values(onlineUsers));
    });

    socket.on('join_room', (room) => {
        socket.join(room);
        userRooms[socket.id] = room;
        console.log(`${socket.username} joined room: ${room}`);
    });

    socket.on('send_message', (data) => {
        const timestamp = new Date().toLocaleTimeString();
        const messageId = ++messageIdCounter;
        const messageData = {
            id: messageId,
            user: socket.username,
            message: data.message,
            timestamp,
            type: data.type || 'text', // Support for different message types (text, file, image)
            fileName: data.fileName || null,
            fileUrl: data.fileUrl || null,
            reactions: {},
            readBy: [socket.username], // Track who has read the message
        };

        // Store message in history for pagination
        messageHistory.push(messageData);

        // Keep only last 1000 messages to prevent memory issues
        if (messageHistory.length > 1000) {
            messageHistory.shift();
        }

        if (data.to) {
            // Private message
            const recipientSocketId = Object.keys(onlineUsers).find(
                id => onlineUsers[id] === data.to
            );
            if (recipientSocketId) {
                messageData.private = true;
                messageData.to = data.to;
                io.to(recipientSocketId).emit('receive_message', messageData);
                socket.emit('receive_message', messageData);
                console.log(`Private message from ${socket.username} to ${data.to}: ${data.message}`);
            }
        } else if (data.room) {
            // Room message
            messageData.room = data.room;
            io.to(data.room).emit('receive_message', messageData);
            console.log(`Room message in ${data.room} from ${socket.username}: ${data.message}`);
        } else {
            // Public message
            io.emit('receive_message', messageData);
            console.log(`Public message from ${socket.username}: ${data.message}`);
        }
    });

    socket.on('typing', ({ isTyping, to, room }) => {
        if (to) {
            // Notify specific user
            const recipientSocketId = Object.keys(onlineUsers).find(
                id => onlineUsers[id] === to
            );
            if (recipientSocketId) {
                io.to(recipientSocketId).emit('user_typing', {
                    user: socket.username,
                    isTyping,
                });
            }
        } else if (room) {
            // Notify users in the room
            socket.to(room).emit('user_typing', {
                user: socket.username,
                isTyping,
            });
        } else {
            // Notify all users except the sender
            socket.broadcast.emit('user_typing', {
                user: socket.username,
                isTyping,
            });
        }
    });

    // Handle message reactions
    socket.on('add_reaction', (data) => {
        const { messageId, reaction } = data;
        const message = messageHistory.find(msg => msg.id === messageId);

        if (message) {
            if (!message.reactions[reaction]) {
                message.reactions[reaction] = [];
            }

            // Remove user's previous reaction to this message
            Object.keys(message.reactions).forEach(reactionType => {
                message.reactions[reactionType] = message.reactions[reactionType].filter(
                    user => user !== socket.username
                );
            });

            // Add new reaction
            message.reactions[reaction].push(socket.username);

            // Broadcast reaction update
            io.emit('reaction_updated', {
                messageId,
                reactions: message.reactions
            });

            console.log(`${socket.username} reacted with ${reaction} to message ${messageId}`);
        }
    });

    // Handle message read receipts
    socket.on('mark_message_read', (data) => {
        const { messageId } = data;
        const message = messageHistory.find(msg => msg.id === messageId);

        if (message && !message.readBy.includes(socket.username)) {
            message.readBy.push(socket.username);

            // Notify message sender about read receipt
            const senderSocketId = Object.keys(onlineUsers).find(
                id => onlineUsers[id] === message.user
            );

            if (senderSocketId) {
                io.to(senderSocketId).emit('message_read', {
                    messageId,
                    readBy: socket.username
                });
            }

            console.log(`${socket.username} read message ${messageId}`);
        }
    });

    // Handle message history request for pagination
    socket.on('get_message_history', (data) => {
        const { offset = 0, limit = 50 } = data;
        const start = Math.max(0, messageHistory.length - offset - limit);
        const end = messageHistory.length - offset;
        const messages = messageHistory.slice(start, end);

        socket.emit('message_history', {
            messages,
            hasMore: start > 0
        });
    });

    socket.on('disconnect', () => {
        console.log(`User disconnected: ${socket.username}`);
        delete onlineUsers[socket.id];
        delete userRooms[socket.id];
        io.emit('online_users', Object.values(onlineUsers));
    });
});

// File upload endpoint
app.post('/upload', upload.single('file'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const fileUrl = `http://localhost:5000/uploads/${req.file.filename}`;
        res.json({
            success: true,
            fileName: req.file.originalname,
            fileUrl: fileUrl,
            fileSize: req.file.size
        });
    } catch (error) {
        res.status(500).json({ error: 'File upload failed' });
    }
});

// Basic test endpoint
app.get('/', (req, res) => {
    res.send('Real-time Chat Server is running...');
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => console.log(`Server running on port ${PORT}`));
