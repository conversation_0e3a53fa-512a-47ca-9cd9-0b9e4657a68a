1.0.0 / 2024-08-31
==================

  * drop node <18
  * allow utf8 as alias for utf-8

0.5.4 / 2021-12-10
==================

  * deps: safe-buffer@5.2.1

0.5.3 / 2018-12-17
==================

  * Use `safe-buffer` for improved Buffer API

0.5.2 / 2016-12-08
==================

  * Fix `parse` to accept any linear whitespace character

0.5.1 / 2016-01-17
==================

  * perf: enable strict mode

0.5.0 / 2014-10-11
==================

  * Add `parse` function

0.4.0 / 2014-09-21
==================

  * Expand non-Unicode `filename` to the full ISO-8859-1 charset

0.3.0 / 2014-09-20
==================

  * Add `fallback` option
  * Add `type` option

0.2.0 / 2014-09-19
==================

  * Reduce ambiguity of file names with hex escape in buggy browsers

0.1.2 / 2014-09-19
==================

  * Fix periodic invalid Unicode filename header

0.1.1 / 2014-09-19
==================

  * Fix invalid characters appearing in `filename*` parameter

0.1.0 / 2014-09-18
==================

  * Make the `filename` argument optional

0.0.0 / 2014-09-18
==================

  * Initial release
