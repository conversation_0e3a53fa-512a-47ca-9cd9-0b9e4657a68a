# 🚀 Real-Time Chat Server

Node.js + Express + Socket.io backend server for the real-time chat application.

## 🛠️ Technology Stack

- **Node.js** - JavaScript runtime environment
- **Express 5.1.0** - Fast, unopinionated web framework
- **Socket.io 4.8.1** - Real-time bidirectional event-based communication
- **Multer** - Middleware for handling multipart/form-data (file uploads)
- **CORS 2.8.5** - Cross-Origin Resource Sharing middleware

## 📦 Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create uploads directory:
   ```bash
   mkdir uploads
   ```

3. Start the server:
   ```bash
   node server.js
   ```

The server will start on `http://localhost:5000`

## 🔧 Configuration

### Environment Variables

Create a `.env` file for custom configuration:

```env
PORT=5000
CORS_ORIGIN=http://localhost:3000
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
```

### Default Settings

- **Port**: 5000
- **CORS Origin**: http://localhost:3000
- **Max File Size**: 10MB
- **Upload Directory**: ./uploads
- **Message History Limit**: 1000 messages

## 🎯 API Endpoints

### REST Endpoints

#### File Upload
```
POST /upload
Content-Type: multipart/form-data

Parameters:
- file: File to upload (max 10MB)

Supported file types:
- Images: jpeg, jpg, png, gif
- Documents: pdf, doc, docx, txt
- Media: mp3, mp4, avi

Response:
{
  "success": true,
  "fileName": "original-filename.ext",
  "fileUrl": "http://localhost:5000/uploads/unique-filename.ext",
  "fileSize": 1024000
}

Error Response:
{
  "error": "Error message"
}
```

#### Health Check
```
GET /
Response: "Real-time Chat Server is running..."
```

#### Static File Serving
```
GET /uploads/:filename
Serves uploaded files statically
```

## 🔌 Socket.io Events

### Client → Server Events

#### User Management
```javascript
// Set username for the session
socket.emit('set_username', 'username');

// Join a specific room
socket.emit('join_room', 'roomName');
```

#### Messaging
```javascript
// Send a public message
socket.emit('send_message', {
    message: 'Hello everyone!',
    type: 'text'
});

// Send a private message
socket.emit('send_message', {
    message: 'Private message',
    type: 'text',
    to: 'recipientUsername'
});

// Send a room message
socket.emit('send_message', {
    message: 'Room message',
    type: 'text',
    room: 'roomName'
});

// Send a file message
socket.emit('send_message', {
    message: 'Shared a file: filename.pdf',
    type: 'file',
    fileName: 'filename.pdf',
    fileUrl: 'http://localhost:5000/uploads/filename.pdf'
});
```

#### Typing Indicators
```javascript
// Send typing indicator
socket.emit('typing', {
    isTyping: true,
    to: 'recipientUsername', // optional for private
    room: 'roomName'         // optional for room
});
```

#### Message Reactions
```javascript
// Add reaction to message
socket.emit('add_reaction', {
    messageId: 123,
    reaction: 'like' // like, love, laugh, wow, sad, angry
});
```

#### Read Receipts
```javascript
// Mark message as read
socket.emit('mark_message_read', {
    messageId: 123
});
```

#### Message History
```javascript
// Request older messages
socket.emit('get_message_history', {
    offset: 0,
    limit: 50
});
```

### Server → Client Events

#### User Updates
```javascript
// Online users list updated
socket.on('online_users', (users) => {
    // users: ['username1', 'username2', ...]
});

// User joined notification
socket.on('user_joined', (username) => {
    // username: string
});

// User left notification
socket.on('user_left', (username) => {
    // username: string
});
```

#### Messages
```javascript
// New message received
socket.on('receive_message', (messageData) => {
    // messageData: {
    //     id: 123,
    //     user: 'sender',
    //     message: 'Hello!',
    //     timestamp: '10:30:00 AM',
    //     type: 'text',
    //     private: false,
    //     to: null,
    //     room: null,
    //     reactions: {},
    //     readBy: ['sender']
    // }
});

// Typing indicator
socket.on('user_typing', ({ user, isTyping }) => {
    // user: string, isTyping: boolean
});
```

#### Reactions & Read Receipts
```javascript
// Message reaction updated
socket.on('reaction_updated', ({ messageId, reactions }) => {
    // messageId: number
    // reactions: { like: ['user1', 'user2'], love: ['user3'] }
});

// Message read receipt
socket.on('message_read', ({ messageId, readBy }) => {
    // messageId: number, readBy: string
});
```

#### Message History
```javascript
// Historical messages response
socket.on('message_history', ({ messages, hasMore }) => {
    // messages: array of message objects
    // hasMore: boolean indicating if more messages available
});
```

## 💾 Data Structures

### Message Object
```javascript
{
    id: 123,                    // Unique message ID
    user: 'username',           // Sender username
    message: 'Hello world!',    // Message content
    timestamp: '10:30:00 AM',   // Formatted timestamp
    type: 'text',               // 'text', 'image', 'video', 'audio', 'file'
    fileName: null,             // Original filename (for files)
    fileUrl: null,              // File URL (for files)
    private: false,             // Is private message
    to: null,                   // Recipient (for private messages)
    room: null,                 // Room name (for room messages)
    reactions: {                // Message reactions
        like: ['user1', 'user2'],
        love: ['user3']
    },
    readBy: ['sender', 'user1'] // Users who read the message
}
```

### User Tracking
```javascript
// Online users: { socketId: username }
onlineUsers = {
    'socket123': 'alice',
    'socket456': 'bob'
};

// User rooms: { socketId: roomName }
userRooms = {
    'socket123': 'general',
    'socket456': 'random'
};
```

## 🔒 Security Features

### File Upload Security
- File type validation (whitelist approach)
- File size limits (10MB default)
- Unique filename generation to prevent conflicts
- Directory traversal protection

### Input Validation
- Message content sanitization
- Username validation
- Room name validation
- File metadata validation

### CORS Configuration
- Configurable allowed origins
- Proper headers for cross-origin requests
- Method restrictions (GET, POST only)

## 📊 Performance Optimizations

### Memory Management
- Message history limited to 1000 messages
- Automatic cleanup of old messages
- Efficient user tracking with object maps

### Socket.io Optimizations
- Room-based message broadcasting
- Efficient event handling
- Connection state management

### File Handling
- Streaming file uploads
- Static file serving with Express
- Proper error handling for file operations

## 🐛 Error Handling

### File Upload Errors
- File size exceeded
- Invalid file type
- Upload directory issues
- Disk space problems

### Socket Connection Errors
- Connection timeouts
- Invalid event data
- User session management
- Room management errors

### Message Processing Errors
- Invalid message format
- Missing required fields
- Database operation failures
- Network communication issues

## 🧪 Testing

### Manual Testing
1. Start the server
2. Connect multiple clients
3. Test all Socket.io events
4. Verify file upload functionality
5. Check error scenarios

### Automated Testing
```bash
# Install testing dependencies
npm install --save-dev jest supertest socket.io-client

# Run tests (when implemented)
npm test
```

## 📈 Monitoring & Logging

### Built-in Logging
- Connection events
- Message sending/receiving
- File upload operations
- Error conditions

### Recommended Monitoring
- Connection count tracking
- Message throughput metrics
- File upload statistics
- Error rate monitoring
- Memory usage tracking

## 🚀 Deployment

### Production Setup
1. Set environment variables
2. Configure reverse proxy (nginx)
3. Set up SSL certificates
4. Configure file upload limits
5. Set up monitoring and logging

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
RUN mkdir -p uploads
EXPOSE 5000
CMD ["node", "server.js"]
```

### Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
PORT=5000
CORS_ORIGIN=https://your-domain.com
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/app/uploads
```

## 🔧 Maintenance

### Regular Tasks
- Clean up old uploaded files
- Monitor disk usage
- Check connection metrics
- Update dependencies
- Review security settings

### Backup Considerations
- Uploaded files backup
- Configuration backup
- Log file rotation
- Database backup (if implemented)

## 📞 Support & Troubleshooting

### Common Issues
1. **CORS errors**: Check CORS_ORIGIN configuration
2. **File upload fails**: Verify upload directory permissions
3. **Connection issues**: Check firewall and port settings
4. **Memory issues**: Monitor message history size

### Debug Mode
Enable detailed logging:
```javascript
// Add to server.js
const debug = require('debug')('chat-server');
debug('Server starting...');
```

---

**Server Status**: ✅ Production Ready
**Last Updated**: 2025-01-17
**Version**: 1.0.0
