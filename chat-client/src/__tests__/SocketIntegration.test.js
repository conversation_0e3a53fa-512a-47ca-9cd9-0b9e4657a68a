// __tests__/SocketIntegration.test.js
import { io } from 'socket.io-client';

// Mock socket.io-client
jest.mock('socket.io-client');

describe('Socket.io Integration', () => {
    let mockSocket;
    let eventHandlers;

    beforeEach(() => {
        eventHandlers = {};
        
        mockSocket = {
            on: jest.fn((event, handler) => {
                eventHandlers[event] = handler;
            }),
            off: jest.fn(),
            emit: jest.fn(),
            connected: true,
            connect: jest.fn(),
            disconnect: jest.fn()
        };

        io.mockReturnValue(mockSocket);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('Connection Events', () => {
        test('handles connection event', () => {
            const onConnect = jest.fn();
            mockSocket.on('connect', onConnect);
            
            // Simulate connection
            eventHandlers.connect && eventHandlers.connect();
            
            expect(onConnect).toHaveBeenCalled();
        });

        test('handles disconnection event', () => {
            const onDisconnect = jest.fn();
            mockSocket.on('disconnect', onDisconnect);
            
            // Simulate disconnection
            eventHandlers.disconnect && eventHandlers.disconnect('transport close');
            
            expect(onDisconnect).toHaveBeenCalledWith('transport close');
        });

        test('handles connection error', () => {
            const onError = jest.fn();
            mockSocket.on('connect_error', onError);
            
            const error = new Error('Connection failed');
            eventHandlers.connect_error && eventHandlers.connect_error(error);
            
            expect(onError).toHaveBeenCalledWith(error);
        });
    });

    describe('Message Events', () => {
        test('handles receive_message event', () => {
            const onMessage = jest.fn();
            mockSocket.on('receive_message', onMessage);
            
            const messageData = {
                id: 1,
                user: 'Alice',
                message: 'Hello world!',
                timestamp: '10:30:00 AM',
                type: 'text'
            };
            
            eventHandlers.receive_message && eventHandlers.receive_message(messageData);
            
            expect(onMessage).toHaveBeenCalledWith(messageData);
        });

        test('handles send_message emission', () => {
            const messageData = {
                message: 'Hello world!',
                type: 'text'
            };
            
            mockSocket.emit('send_message', messageData);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('send_message', messageData);
        });

        test('handles private message emission', () => {
            const messageData = {
                message: 'Private message',
                type: 'text',
                to: 'Alice'
            };
            
            mockSocket.emit('send_message', messageData);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('send_message', messageData);
        });

        test('handles room message emission', () => {
            const messageData = {
                message: 'Room message',
                type: 'text',
                room: 'general'
            };
            
            mockSocket.emit('send_message', messageData);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('send_message', messageData);
        });
    });

    describe('User Events', () => {
        test('handles online_users event', () => {
            const onUsersUpdate = jest.fn();
            mockSocket.on('online_users', onUsersUpdate);
            
            const users = ['Alice', 'Bob', 'Charlie'];
            eventHandlers.online_users && eventHandlers.online_users(users);
            
            expect(onUsersUpdate).toHaveBeenCalledWith(users);
        });

        test('handles set_username emission', () => {
            const username = 'TestUser';
            
            mockSocket.emit('set_username', username);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('set_username', username);
        });

        test('handles user_typing event', () => {
            const onTyping = jest.fn();
            mockSocket.on('user_typing', onTyping);
            
            const typingData = { user: 'Alice', isTyping: true };
            eventHandlers.user_typing && eventHandlers.user_typing(typingData);
            
            expect(onTyping).toHaveBeenCalledWith(typingData);
        });

        test('handles typing emission', () => {
            const typingData = {
                isTyping: true,
                to: '',
                room: 'global'
            };
            
            mockSocket.emit('typing', typingData);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('typing', typingData);
        });
    });

    describe('Room Events', () => {
        test('handles join_room emission', () => {
            const roomName = 'general';
            
            mockSocket.emit('join_room', roomName);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('join_room', roomName);
        });

        test('handles user_joined event', () => {
            const onUserJoined = jest.fn();
            mockSocket.on('user_joined', onUserJoined);
            
            const username = 'Alice';
            eventHandlers.user_joined && eventHandlers.user_joined(username);
            
            expect(onUserJoined).toHaveBeenCalledWith(username);
        });

        test('handles user_left event', () => {
            const onUserLeft = jest.fn();
            mockSocket.on('user_left', onUserLeft);
            
            const username = 'Alice';
            eventHandlers.user_left && eventHandlers.user_left(username);
            
            expect(onUserLeft).toHaveBeenCalledWith(username);
        });
    });

    describe('Reaction Events', () => {
        test('handles add_reaction emission', () => {
            const reactionData = {
                messageId: 1,
                reaction: 'like'
            };
            
            mockSocket.emit('add_reaction', reactionData);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('add_reaction', reactionData);
        });

        test('handles reaction_updated event', () => {
            const onReactionUpdate = jest.fn();
            mockSocket.on('reaction_updated', onReactionUpdate);
            
            const reactionData = {
                messageId: 1,
                reactions: { like: ['Alice', 'Bob'] }
            };
            
            eventHandlers.reaction_updated && eventHandlers.reaction_updated(reactionData);
            
            expect(onReactionUpdate).toHaveBeenCalledWith(reactionData);
        });
    });

    describe('Read Receipt Events', () => {
        test('handles mark_message_read emission', () => {
            const readData = { messageId: 1 };
            
            mockSocket.emit('mark_message_read', readData);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('mark_message_read', readData);
        });

        test('handles message_read event', () => {
            const onMessageRead = jest.fn();
            mockSocket.on('message_read', onMessageRead);
            
            const readData = {
                messageId: 1,
                readBy: 'Alice'
            };
            
            eventHandlers.message_read && eventHandlers.message_read(readData);
            
            expect(onMessageRead).toHaveBeenCalledWith(readData);
        });
    });

    describe('Message History Events', () => {
        test('handles get_message_history emission', () => {
            const historyRequest = {
                offset: 0,
                limit: 50
            };
            
            mockSocket.emit('get_message_history', historyRequest);
            
            expect(mockSocket.emit).toHaveBeenCalledWith('get_message_history', historyRequest);
        });

        test('handles message_history event', () => {
            const onMessageHistory = jest.fn();
            mockSocket.on('message_history', onMessageHistory);
            
            const historyData = {
                messages: [
                    { id: 1, user: 'Alice', message: 'Old message' }
                ],
                hasMore: true
            };
            
            eventHandlers.message_history && eventHandlers.message_history(historyData);
            
            expect(onMessageHistory).toHaveBeenCalledWith(historyData);
        });
    });

    describe('Reconnection Events', () => {
        test('handles reconnect event', () => {
            const onReconnect = jest.fn();
            mockSocket.on('reconnect', onReconnect);
            
            const attemptNumber = 3;
            eventHandlers.reconnect && eventHandlers.reconnect(attemptNumber);
            
            expect(onReconnect).toHaveBeenCalledWith(attemptNumber);
        });

        test('handles reconnect_error event', () => {
            const onReconnectError = jest.fn();
            mockSocket.on('reconnect_error', onReconnectError);
            
            const error = new Error('Reconnection failed');
            eventHandlers.reconnect_error && eventHandlers.reconnect_error(error);
            
            expect(onReconnectError).toHaveBeenCalledWith(error);
        });

        test('handles reconnect_failed event', () => {
            const onReconnectFailed = jest.fn();
            mockSocket.on('reconnect_failed', onReconnectFailed);
            
            eventHandlers.reconnect_failed && eventHandlers.reconnect_failed();
            
            expect(onReconnectFailed).toHaveBeenCalled();
        });
    });
});
