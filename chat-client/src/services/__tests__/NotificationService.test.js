// services/__tests__/NotificationService.test.js
import NotificationService from '../NotificationService';

// Mock the Notification API
const mockNotification = jest.fn();
mockNotification.requestPermission = jest.fn();
mockNotification.permission = 'default';

Object.defineProperty(window, 'Notification', {
    value: mockNotification,
    writable: true
});

// Mock Audio
const mockAudio = {
    play: jest.fn().mockResolvedValue(undefined),
    pause: jest.fn(),
    volume: 0.5,
    currentTime: 0
};

// Create a constructor function that returns the mock
const MockAudio = jest.fn().mockImplementation(() => mockAudio);

Object.defineProperty(window, 'Audio', {
    value: MockAudio,
    writable: true
});

describe('NotificationService', () => {
    let notificationService;

    beforeEach(() => {
        jest.clearAllMocks();
        mockNotification.mockClear();
        mockNotification.requestPermission.mockClear();
        MockAudio.mockClear();
        mockAudio.play.mockClear();

        // Reset permission
        mockNotification.permission = 'default';

        // Create new instance for each test
        notificationService = new (require('../NotificationService').default.constructor)();
    });

    describe('initialization', () => {
        test('detects notification support', () => {
            expect(notificationService.isSupported).toBe(true);
        });

        test('sets initial permission status', () => {
            expect(notificationService.permission).toBe('default');
        });

        test('initializes sound objects', () => {
            expect(notificationService.sounds).toHaveProperty('message');
            expect(notificationService.sounds).toHaveProperty('join');
            expect(notificationService.sounds).toHaveProperty('leave');
            expect(notificationService.sounds).toHaveProperty('mention');
        });
    });

    describe('requestPermission', () => {
        test('requests permission when default', async () => {
            mockNotification.requestPermission.mockResolvedValue('granted');
            
            const result = await notificationService.requestPermission();
            
            expect(mockNotification.requestPermission).toHaveBeenCalled();
            expect(result).toBe('granted');
            expect(notificationService.permission).toBe('granted');
        });

        test('returns current permission when already set', async () => {
            notificationService.permission = 'granted';
            
            const result = await notificationService.requestPermission();
            
            expect(mockNotification.requestPermission).not.toHaveBeenCalled();
            expect(result).toBe('granted');
        });

        test('returns denied when not supported', async () => {
            notificationService.isSupported = false;
            
            const result = await notificationService.requestPermission();
            
            expect(result).toBe('denied');
        });
    });

    describe('showNotification', () => {
        test('creates notification when permission granted', () => {
            notificationService.permission = 'granted';
            
            notificationService.showNotification('Test Title', { body: 'Test Body' });
            
            expect(mockNotification).toHaveBeenCalledWith('Test Title', {
                icon: '/logo192.png',
                badge: '/logo192.png',
                tag: 'chat-notification',
                requireInteraction: false,
                body: 'Test Body'
            });
        });

        test('does not create notification when permission denied', () => {
            notificationService.permission = 'denied';
            
            notificationService.showNotification('Test Title');
            
            expect(mockNotification).not.toHaveBeenCalled();
        });

        test('does not create notification when not supported', () => {
            notificationService.isSupported = false;
            
            notificationService.showNotification('Test Title');
            
            expect(mockNotification).not.toHaveBeenCalled();
        });
    });

    describe('playSound', () => {
        test('plays message sound by default', () => {
            notificationService.playSound();
            
            expect(mockAudio.currentTime).toBe(0);
            expect(mockAudio.play).toHaveBeenCalled();
        });

        test('plays specific sound type', () => {
            notificationService.playSound('join');
            
            expect(mockAudio.play).toHaveBeenCalled();
        });

        test('handles sound play errors gracefully', () => {
            mockAudio.play.mockRejectedValue(new Error('Play failed'));
            
            expect(() => notificationService.playSound()).not.toThrow();
        });
    });

    describe('notifyNewMessage', () => {
        beforeEach(() => {
            notificationService.permission = 'granted';
            notificationService.showNotification = jest.fn();
            notificationService.playSound = jest.fn();
        });

        test('does not notify for own messages', () => {
            const message = { user: 'TestUser', message: 'Hello' };
            
            notificationService.notifyNewMessage(message, 'TestUser');
            
            expect(notificationService.showNotification).not.toHaveBeenCalled();
            expect(notificationService.playSound).not.toHaveBeenCalled();
        });

        test('notifies for private messages', () => {
            const message = {
                user: 'Alice',
                message: 'Private message',
                private: true,
                id: 1
            };
            
            notificationService.notifyNewMessage(message, 'TestUser');
            
            expect(notificationService.showNotification).toHaveBeenCalledWith(
                'Private message from Alice',
                expect.objectContaining({
                    body: 'Private message',
                    tag: 'message-1'
                })
            );
            expect(notificationService.playSound).toHaveBeenCalledWith('mention');
        });

        test('notifies for mentions', () => {
            const message = {
                user: 'Alice',
                message: 'Hey @TestUser, how are you?',
                id: 2
            };
            
            notificationService.notifyNewMessage(message, 'TestUser');
            
            expect(notificationService.showNotification).toHaveBeenCalledWith(
                'Alice mentioned you',
                expect.objectContaining({
                    body: 'Hey @TestUser, how are you?'
                })
            );
            expect(notificationService.playSound).toHaveBeenCalledWith('mention');
        });

        test('notifies for regular messages', () => {
            const message = {
                user: 'Alice',
                message: 'Hello everyone!',
                id: 3
            };
            
            notificationService.notifyNewMessage(message, 'TestUser');
            
            expect(notificationService.showNotification).toHaveBeenCalledWith(
                'New message from Alice',
                expect.objectContaining({
                    body: 'Hello everyone!'
                })
            );
            expect(notificationService.playSound).toHaveBeenCalledWith('message');
        });

        test('truncates long messages', () => {
            const longMessage = 'A'.repeat(150);
            const message = {
                user: 'Alice',
                message: longMessage,
                id: 4
            };
            
            notificationService.notifyNewMessage(message, 'TestUser');
            
            expect(notificationService.showNotification).toHaveBeenCalledWith(
                'New message from Alice',
                expect.objectContaining({
                    body: longMessage.substring(0, 100) + '...'
                })
            );
        });
    });

    describe('sound management', () => {
        test('sets sound volume', () => {
            notificationService.setSoundVolume(0.8);
            
            Object.values(notificationService.sounds).forEach(sound => {
                expect(sound.volume).toBe(0.8);
            });
        });

        test('clamps volume to valid range', () => {
            notificationService.setSoundVolume(1.5);
            
            Object.values(notificationService.sounds).forEach(sound => {
                expect(sound.volume).toBe(1);
            });
        });

        test('disables sounds when setSoundsEnabled(false)', () => {
            notificationService.setSoundsEnabled(false);
            
            Object.values(notificationService.sounds).forEach(sound => {
                expect(sound.volume).toBe(0);
            });
        });

        test('enables sounds when setSoundsEnabled(true)', () => {
            notificationService.setSoundsEnabled(true);
            
            Object.values(notificationService.sounds).forEach(sound => {
                expect(sound.volume).toBe(0.5);
            });
        });
    });

    describe('utility methods', () => {
        test('canShowNotifications returns correct status', () => {
            notificationService.permission = 'granted';
            expect(notificationService.canShowNotifications()).toBe(true);
            
            notificationService.permission = 'denied';
            expect(notificationService.canShowNotifications()).toBe(false);
        });

        test('getPermissionStatus returns current permission', () => {
            notificationService.permission = 'granted';
            expect(notificationService.getPermissionStatus()).toBe('granted');
        });
    });
});
