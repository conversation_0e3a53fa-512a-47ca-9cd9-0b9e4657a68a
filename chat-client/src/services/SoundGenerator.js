// services/SoundGenerator.js

/**
 * SoundGenerator
 * Generates notification sounds programmatically using Web Audio API
 * This is a fallback when audio files are not available
 */
class SoundGenerator {
    constructor() {
        this.audioContext = null;
        this.isSupported = 'AudioContext' in window || 'webkitAudioContext' in window;
        
        if (this.isSupported) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
    }

    /**
     * Create a simple beep sound
     * @param {number} frequency - Frequency in Hz
     * @param {number} duration - Duration in seconds
     * @param {number} volume - Volume (0-1)
     */
    createBeep(frequency = 800, duration = 0.2, volume = 0.3) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    /**
     * Play message notification sound
     */
    playMessageSound() {
        this.createBeep(800, 0.15, 0.3);
    }

    /**
     * Play join notification sound
     */
    playJoinSound() {
        this.createBeep(600, 0.1, 0.2);
        setTimeout(() => this.createBeep(800, 0.1, 0.2), 100);
    }

    /**
     * Play leave notification sound
     */
    playLeaveSound() {
        this.createBeep(800, 0.1, 0.2);
        setTimeout(() => this.createBeep(600, 0.1, 0.2), 100);
    }

    /**
     * Play mention notification sound (more prominent)
     */
    playMentionSound() {
        this.createBeep(1000, 0.1, 0.4);
        setTimeout(() => this.createBeep(800, 0.1, 0.4), 150);
        setTimeout(() => this.createBeep(1000, 0.1, 0.4), 300);
    }

    /**
     * Resume audio context (required for some browsers)
     */
    resumeContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }
}

// Create and export a singleton instance
const soundGenerator = new SoundGenerator();
export default soundGenerator;
