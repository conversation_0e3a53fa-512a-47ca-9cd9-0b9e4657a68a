// services/NotificationService.js

/**
 * NotificationService
 * Handles browser notifications, sound alerts, and notification permissions
 */
class NotificationService {
    constructor() {
        this.isSupported = 'Notification' in window;
        this.permission = this.isSupported ? Notification.permission : 'denied';
        this.sounds = {
            message: new Audio('/sounds/message.mp3'),
            join: new Audio('/sounds/join.mp3'),
            leave: new Audio('/sounds/leave.mp3'),
            mention: new Audio('/sounds/mention.mp3')
        };
        
        // Set default volume
        Object.values(this.sounds).forEach(sound => {
            sound.volume = 0.5;
        });
    }

    /**
     * Request notification permission from the user
     * @returns {Promise<string>} Permission status
     */
    async requestPermission() {
        if (!this.isSupported) {
            return 'denied';
        }

        if (this.permission === 'default') {
            this.permission = await Notification.requestPermission();
        }

        return this.permission;
    }

    /**
     * Show a browser notification
     * @param {string} title - Notification title
     * @param {Object} options - Notification options
     */
    showNotification(title, options = {}) {
        if (!this.isSupported || this.permission !== 'granted') {
            return;
        }

        const defaultOptions = {
            icon: '/logo192.png',
            badge: '/logo192.png',
            tag: 'chat-notification',
            requireInteraction: false,
            ...options
        };

        const notification = new Notification(title, defaultOptions);

        // Auto-close after 5 seconds
        setTimeout(() => {
            notification.close();
        }, 5000);

        // Focus window when notification is clicked
        notification.onclick = () => {
            window.focus();
            notification.close();
        };

        return notification;
    }

    /**
     * Play a sound notification
     * @param {string} type - Sound type (message, join, leave, mention)
     */
    playSound(type = 'message') {
        try {
            const sound = this.sounds[type];
            if (sound) {
                sound.currentTime = 0; // Reset to beginning
                sound.play().catch(error => {
                    console.warn('Could not play notification sound:', error);
                });
            }
        } catch (error) {
            console.warn('Sound notification failed:', error);
        }
    }

    /**
     * Show notification for new message
     * @param {Object} message - Message object
     * @param {string} currentUser - Current user's username
     */
    notifyNewMessage(message, currentUser) {
        // Don't notify for own messages
        if (message.user === currentUser) {
            return;
        }

        const isPrivate = message.private;
        const isMention = message.message.toLowerCase().includes(`@${currentUser.toLowerCase()}`);
        
        let title, body, soundType;

        if (isPrivate) {
            title = `Private message from ${message.user}`;
            body = message.message;
            soundType = 'mention';
        } else if (isMention) {
            title = `${message.user} mentioned you`;
            body = message.message;
            soundType = 'mention';
        } else {
            title = `New message from ${message.user}`;
            body = message.room ? `[${message.room}] ${message.message}` : message.message;
            soundType = 'message';
        }

        // Show browser notification
        this.showNotification(title, {
            body: body.length > 100 ? body.substring(0, 100) + '...' : body,
            tag: `message-${message.id}`,
            data: { messageId: message.id, type: 'message' }
        });

        // Play sound
        this.playSound(soundType);
    }

    /**
     * Show notification for user joining
     * @param {string} username - Username of joining user
     */
    notifyUserJoined(username) {
        this.showNotification('User Joined', {
            body: `${username} joined the chat`,
            tag: 'user-joined',
            data: { type: 'join', username }
        });

        this.playSound('join');
    }

    /**
     * Show notification for user leaving
     * @param {string} username - Username of leaving user
     */
    notifyUserLeft(username) {
        this.showNotification('User Left', {
            body: `${username} left the chat`,
            tag: 'user-left',
            data: { type: 'leave', username }
        });

        this.playSound('leave');
    }

    /**
     * Set sound volume
     * @param {number} volume - Volume level (0-1)
     */
    setSoundVolume(volume) {
        Object.values(this.sounds).forEach(sound => {
            sound.volume = Math.max(0, Math.min(1, volume));
        });
    }

    /**
     * Enable or disable sounds
     * @param {boolean} enabled - Whether sounds should be enabled
     */
    setSoundsEnabled(enabled) {
        if (!enabled) {
            Object.values(this.sounds).forEach(sound => {
                sound.volume = 0;
            });
        } else {
            this.setSoundVolume(0.5); // Reset to default volume
        }
    }

    /**
     * Check if notifications are supported and permitted
     * @returns {boolean} Whether notifications can be shown
     */
    canShowNotifications() {
        return this.isSupported && this.permission === 'granted';
    }

    /**
     * Get current permission status
     * @returns {string} Permission status
     */
    getPermissionStatus() {
        return this.permission;
    }
}

// Create and export a singleton instance
const notificationService = new NotificationService();
export default notificationService;
