// components/MessageReactions.js
import React from 'react';

/**
 * MessageReactions Component
 * Displays and handles message reactions (like, love, laugh, etc.)
 * 
 * @param {Object} props - Component props
 * @param {Object} props.reactions - Object containing reaction counts
 * @param {number} props.messageId - Unique message identifier
 * @param {Function} props.onAddReaction - Callback for adding reactions
 * @param {string} props.currentUser - Current user's username
 */
function MessageReactions({ reactions, messageId, onAddReaction, currentUser }) {
    // Available reaction emojis
    const availableReactions = [
        { emoji: '👍', name: 'like' },
        { emoji: '❤️', name: 'love' },
        { emoji: '😂', name: 'laugh' },
        { emoji: '😮', name: 'wow' },
        { emoji: '😢', name: 'sad' },
        { emoji: '😡', name: 'angry' }
    ];

    // Handle reaction click
    const handleReactionClick = (reactionName) => {
        onAddReaction(messageId, reactionName);
    };

    // Get user's current reaction
    const getUserReaction = () => {
        for (const [reactionType, users] of Object.entries(reactions || {})) {
            if (users.includes(currentUser)) {
                return reactionType;
            }
        }
        return null;
    };

    const userReaction = getUserReaction();

    return (
        <div className="message-reactions">
            {/* Display existing reactions */}
            <div className="existing-reactions">
                {Object.entries(reactions || {}).map(([reactionType, users]) => {
                    if (users.length === 0) return null;
                    
                    const reactionEmoji = availableReactions.find(r => r.name === reactionType)?.emoji || '👍';
                    const isUserReaction = users.includes(currentUser);
                    
                    return (
                        <span
                            key={reactionType}
                            className={`reaction-badge ${isUserReaction ? 'user-reaction' : ''}`}
                            onClick={() => handleReactionClick(reactionType)}
                            title={`${users.join(', ')} reacted with ${reactionType}`}
                        >
                            {reactionEmoji} {users.length}
                        </span>
                    );
                })}
            </div>

            {/* Reaction picker */}
            <div className="reaction-picker">
                <button className="add-reaction-btn" title="Add reaction">
                    😊
                </button>
                <div className="reaction-options">
                    {availableReactions.map((reaction) => (
                        <button
                            key={reaction.name}
                            className={`reaction-option ${userReaction === reaction.name ? 'selected' : ''}`}
                            onClick={() => handleReactionClick(reaction.name)}
                            title={reaction.name}
                        >
                            {reaction.emoji}
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
}

export default MessageReactions;
