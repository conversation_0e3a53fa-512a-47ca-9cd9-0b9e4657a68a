// components/MessageInput.js
import React, { useState } from 'react';
import FileUpload from './FileUpload';

/**
 * MessageInput Component
 * Enhanced message input with file upload support and typing indicators
 *
 * @param {Object} props - Component props
 * @param {string} props.message - Current message text
 * @param {Function} props.setMessage - Function to update message text
 * @param {Object} props.socket - Socket.io client instance
 * @param {string} props.recipient - Current private message recipient
 * @param {string} props.currentRoom - Current chat room
 */
function MessageInput({ message, setMessage, socket, recipient, currentRoom }) {
    const [isUploading, setIsUploading] = useState(false);

    // Handle text message sending
    const handleSend = (e) => {
        e.preventDefault();
        if (!message.trim() || isUploading) return;

        sendMessage({ message, type: 'text' });
        setMessage('');
        socket.emit('typing', { isTyping: false, to: recipient, room: currentRoom });
    };

    // Handle typing indicator
    const handleTyping = (e) => {
        setMessage(e.target.value);
        socket.emit('typing', { isTyping: true, to: recipient, room: currentRoom });
    };

    // Generic message sending function
    const sendMessage = (messageData) => {
        const payload = { ...messageData };

        // Add recipient or room information
        if (recipient) {
            payload.to = recipient;
        } else if (currentRoom && currentRoom !== 'global') {
            payload.room = currentRoom;
        }

        socket.emit('send_message', payload);
    };

    // Handle file upload completion
    const handleFileUpload = (fileData) => {
        const fileMessage = {
            message: `Shared a file: ${fileData.fileName}`,
            type: fileData.type,
            fileName: fileData.fileName,
            fileUrl: fileData.fileUrl
        };

        sendMessage(fileMessage);
        setIsUploading(false);
    };

    // Handle upload start
    const handleUploadStart = () => {
        setIsUploading(true);
    };

    // Handle upload error
    const handleUploadError = (error) => {
        console.error('Upload error:', error);
        alert(`Upload failed: ${error}`);
        setIsUploading(false);
    };

    return (
        <div className="message-input-container">
            <form onSubmit={handleSend} className="message-input-form">
                <div className="input-group">
                    <input
                        type="text"
                        value={message}
                        onChange={handleTyping}
                        placeholder={isUploading ? "Uploading file..." : "Type a message..."}
                        className="message-input-box"
                        disabled={isUploading}
                    />

                    <FileUpload
                        onFileUpload={handleFileUpload}
                        onUploadStart={handleUploadStart}
                        onUploadError={handleUploadError}
                    />

                    <button
                        type="submit"
                        className="message-send-btn"
                        disabled={isUploading || !message.trim()}
                    >
                        {isUploading ? '⏳' : 'Send'}
                    </button>
                </div>
            </form>
        </div>
    );
}

export default MessageInput;
