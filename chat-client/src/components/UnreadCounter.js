// components/UnreadCounter.js
import React, { useState, useEffect } from 'react';

/**
 * UnreadCounter Component
 * Displays unread message counts and manages read status
 * 
 * @param {Object} props - Component props
 * @param {Array} props.messages - Array of all messages
 * @param {string} props.currentUser - Current user's username
 * @param {string} props.currentRoom - Current active room
 * @param {string} props.recipient - Current private message recipient
 * @param {boolean} props.isWindowFocused - Whether the window is currently focused
 */
function UnreadCounter({ messages, currentUser, currentRoom, recipient, isWindowFocused }) {
    const [unreadCounts, setUnreadCounts] = useState({
        global: 0,
        private: 0,
        rooms: {}
    });
    const [lastReadTimestamp, setLastReadTimestamp] = useState(Date.now());

    // Update unread counts when messages change
    useEffect(() => {
        if (!messages || messages.length === 0) return;

        const counts = {
            global: 0,
            private: 0,
            rooms: {}
        };

        // Count unread messages
        messages.forEach(message => {
            // <PERSON>p own messages
            if (message.user === currentUser) return;

            // Check if message is newer than last read timestamp
            const messageTime = new Date(message.timestamp).getTime() || Date.now();
            if (messageTime <= lastReadTimestamp) return;

            if (message.private) {
                // Private message
                if (message.to === currentUser || message.user === currentUser) {
                    counts.private++;
                }
            } else if (message.room) {
                // Room message
                if (!counts.rooms[message.room]) {
                    counts.rooms[message.room] = 0;
                }
                counts.rooms[message.room]++;
            } else {
                // Global message
                counts.global++;
            }
        });

        setUnreadCounts(counts);
    }, [messages, currentUser, lastReadTimestamp]);

    // Mark messages as read when window is focused and user is in a specific context
    useEffect(() => {
        if (isWindowFocused) {
            setLastReadTimestamp(Date.now());
        }
    }, [isWindowFocused, currentRoom, recipient]);

    // Get total unread count
    const getTotalUnread = () => {
        const roomCounts = Object.values(unreadCounts.rooms).reduce((sum, count) => sum + count, 0);
        return unreadCounts.global + unreadCounts.private + roomCounts;
    };

    // Get unread count for current context
    const getCurrentContextUnread = () => {
        if (recipient) {
            return unreadCounts.private;
        } else if (currentRoom && currentRoom !== 'global') {
            return unreadCounts.rooms[currentRoom] || 0;
        } else {
            return unreadCounts.global;
        }
    };

    // Update document title with unread count
    useEffect(() => {
        const totalUnread = getTotalUnread();
        const originalTitle = 'Real-Time Chat';
        
        if (totalUnread > 0) {
            document.title = `(${totalUnread}) ${originalTitle}`;
        } else {
            document.title = originalTitle;
        }

        // Cleanup on unmount
        return () => {
            document.title = originalTitle;
        };
    }, [unreadCounts]);

    const totalUnread = getTotalUnread();
    const currentContextUnread = getCurrentContextUnread();

    if (totalUnread === 0) return null;

    return (
        <div className="unread-counter-container">
            {/* Total unread badge */}
            {totalUnread > 0 && (
                <div className="unread-badge total-unread" title={`${totalUnread} unread messages`}>
                    {totalUnread > 99 ? '99+' : totalUnread}
                </div>
            )}

            {/* Context-specific unread indicator */}
            {currentContextUnread > 0 && (
                <div className="unread-indicator">
                    <span className="unread-text">
                        {currentContextUnread} unread message{currentContextUnread !== 1 ? 's' : ''}
                    </span>
                </div>
            )}

            {/* Detailed breakdown */}
            <div className="unread-breakdown">
                {unreadCounts.global > 0 && (
                    <div className="unread-item">
                        <span className="unread-label">Global:</span>
                        <span className="unread-count">{unreadCounts.global}</span>
                    </div>
                )}
                
                {unreadCounts.private > 0 && (
                    <div className="unread-item">
                        <span className="unread-label">Private:</span>
                        <span className="unread-count">{unreadCounts.private}</span>
                    </div>
                )}
                
                {Object.entries(unreadCounts.rooms).map(([room, count]) => {
                    if (count === 0) return null;
                    return (
                        <div key={room} className="unread-item">
                            <span className="unread-label">{room}:</span>
                            <span className="unread-count">{count}</span>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}

export default UnreadCounter;
