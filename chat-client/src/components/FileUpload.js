// components/FileUpload.js
import React, { useState } from 'react';

/**
 * FileUpload Component
 * Handles file uploads for the chat application
 * Supports images, documents, and other file types
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onFileUpload - Callback when file is uploaded successfully
 * @param {Function} props.onUploadStart - Callback when upload starts
 * @param {Function} props.onUploadError - Callback when upload fails
 */
function FileUpload({ onFileUpload, onUploadStart, onUploadError }) {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);

    // Handle file selection and upload
    const handleFileSelect = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file size (10MB limit)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            onUploadError && onUploadError('File size must be less than 10MB');
            return;
        }

        // Validate file type
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
            'application/pdf', 'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain', 'audio/mpeg', 'video/mp4', 'video/avi'
        ];

        if (!allowedTypes.includes(file.type)) {
            onUploadError && onUploadError('File type not supported');
            return;
        }

        setIsUploading(true);
        setUploadProgress(0);
        onUploadStart && onUploadStart();

        try {
            const formData = new FormData();
            formData.append('file', file);

            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();

            // Track upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const progress = Math.round((e.loaded / e.total) * 100);
                    setUploadProgress(progress);
                }
            });

            // Handle upload completion
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    onFileUpload && onFileUpload({
                        fileName: response.fileName,
                        fileUrl: response.fileUrl,
                        fileSize: response.fileSize,
                        type: getFileType(file.type)
                    });
                } else {
                    onUploadError && onUploadError('Upload failed');
                }
                setIsUploading(false);
                setUploadProgress(0);
            });

            // Handle upload error
            xhr.addEventListener('error', () => {
                onUploadError && onUploadError('Upload failed');
                setIsUploading(false);
                setUploadProgress(0);
            });

            // Start upload
            xhr.open('POST', 'http://localhost:5000/upload');
            xhr.send(formData);

        } catch (error) {
            console.error('Upload error:', error);
            onUploadError && onUploadError('Upload failed');
            setIsUploading(false);
            setUploadProgress(0);
        }

        // Clear the input
        event.target.value = '';
    };

    // Determine file type for display
    const getFileType = (mimeType) => {
        if (mimeType.startsWith('image/')) return 'image';
        if (mimeType.startsWith('video/')) return 'video';
        if (mimeType.startsWith('audio/')) return 'audio';
        return 'file';
    };

    return (
        <div className="file-upload-container">
            <input
                type="file"
                id="file-input"
                onChange={handleFileSelect}
                disabled={isUploading}
                style={{ display: 'none' }}
                accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
            />
            
            <label 
                htmlFor="file-input" 
                className={`file-upload-btn ${isUploading ? 'uploading' : ''}`}
                title="Upload file"
            >
                {isUploading ? (
                    <div className="upload-progress">
                        <span>📤</span>
                        <span className="progress-text">{uploadProgress}%</span>
                    </div>
                ) : (
                    <span>📎</span>
                )}
            </label>

            {isUploading && (
                <div className="upload-progress-bar">
                    <div 
                        className="progress-fill" 
                        style={{ width: `${uploadProgress}%` }}
                    ></div>
                </div>
            )}
        </div>
    );
}

export default FileUpload;
