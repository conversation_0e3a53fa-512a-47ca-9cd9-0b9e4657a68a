// components/MessageSearch.js
import React, { useState, useEffect, useMemo } from 'react';

/**
 * MessageSearch Component
 * Provides search functionality for chat messages with filters and highlighting
 * 
 * @param {Object} props - Component props
 * @param {Array} props.messages - Array of all messages
 * @param {Function} props.onSearchResults - Callback with search results
 * @param {boolean} props.isOpen - Whether search panel is open
 * @param {Function} props.onClose - Callback to close search panel
 */
function MessageSearch({ messages, onSearchResults, isOpen, onClose }) {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchFilters, setSearchFilters] = useState({
        user: '',
        room: '',
        type: 'all', // all, text, file, image
        dateFrom: '',
        dateTo: ''
    });
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);

    // Perform search when query or filters change
    const filteredResults = useMemo(() => {
        if (!searchQuery.trim() && !Object.values(searchFilters).some(filter => filter && filter !== 'all')) {
            return [];
        }

        setIsSearching(true);

        const results = messages.filter(message => {
            // Text search
            if (searchQuery.trim()) {
                const query = searchQuery.toLowerCase();
                const messageText = message.message.toLowerCase();
                const userName = message.user.toLowerCase();
                const fileName = (message.fileName || '').toLowerCase();
                
                if (!messageText.includes(query) && !userName.includes(query) && !fileName.includes(query)) {
                    return false;
                }
            }

            // User filter
            if (searchFilters.user && !message.user.toLowerCase().includes(searchFilters.user.toLowerCase())) {
                return false;
            }

            // Room filter
            if (searchFilters.room) {
                if (searchFilters.room === 'global' && message.room) {
                    return false;
                }
                if (searchFilters.room === 'private' && !message.private) {
                    return false;
                }
                if (searchFilters.room !== 'global' && searchFilters.room !== 'private' && 
                    message.room !== searchFilters.room) {
                    return false;
                }
            }

            // Type filter
            if (searchFilters.type !== 'all') {
                if (searchFilters.type === 'text' && message.type !== 'text') {
                    return false;
                }
                if (searchFilters.type === 'file' && message.type === 'text') {
                    return false;
                }
                if (searchFilters.type === 'image' && !['image'].includes(message.type)) {
                    return false;
                }
            }

            // Date filters
            if (searchFilters.dateFrom || searchFilters.dateTo) {
                const messageDate = new Date(message.timestamp).toDateString();
                
                if (searchFilters.dateFrom) {
                    const fromDate = new Date(searchFilters.dateFrom).toDateString();
                    if (messageDate < fromDate) {
                        return false;
                    }
                }
                
                if (searchFilters.dateTo) {
                    const toDate = new Date(searchFilters.dateTo).toDateString();
                    if (messageDate > toDate) {
                        return false;
                    }
                }
            }

            return true;
        });

        setIsSearching(false);
        return results.reverse(); // Show newest first
    }, [messages, searchQuery, searchFilters]);

    // Update search results
    useEffect(() => {
        setSearchResults(filteredResults);
        onSearchResults && onSearchResults(filteredResults);
    }, [filteredResults, onSearchResults]);

    // Clear search
    const handleClearSearch = () => {
        setSearchQuery('');
        setSearchFilters({
            user: '',
            room: '',
            type: 'all',
            dateFrom: '',
            dateTo: ''
        });
    };

    // Highlight search terms in text
    const highlightText = (text, query) => {
        if (!query.trim()) return text;
        
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        const parts = text.split(regex);
        
        return parts.map((part, index) => 
            regex.test(part) ? 
                <mark key={index} className="search-highlight">{part}</mark> : 
                part
        );
    };

    if (!isOpen) return null;

    return (
        <div className="message-search-overlay">
            <div className="message-search-panel">
                <div className="search-header">
                    <h3>Search Messages</h3>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>

                <div className="search-controls">
                    {/* Main search input */}
                    <div className="search-input-group">
                        <input
                            type="text"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            placeholder="Search messages, users, or files..."
                            className="search-input"
                        />
                        {(searchQuery || Object.values(searchFilters).some(f => f && f !== 'all')) && (
                            <button className="clear-search-btn" onClick={handleClearSearch}>
                                Clear
                            </button>
                        )}
                    </div>

                    {/* Search filters */}
                    <div className="search-filters">
                        <div className="filter-group">
                            <label>User:</label>
                            <input
                                type="text"
                                value={searchFilters.user}
                                onChange={(e) => setSearchFilters(prev => ({ ...prev, user: e.target.value }))}
                                placeholder="Filter by user"
                                className="filter-input"
                            />
                        </div>

                        <div className="filter-group">
                            <label>Room:</label>
                            <select
                                value={searchFilters.room}
                                onChange={(e) => setSearchFilters(prev => ({ ...prev, room: e.target.value }))}
                                className="filter-select"
                            >
                                <option value="">All rooms</option>
                                <option value="global">Global</option>
                                <option value="private">Private messages</option>
                                {/* Add dynamic room options here */}
                            </select>
                        </div>

                        <div className="filter-group">
                            <label>Type:</label>
                            <select
                                value={searchFilters.type}
                                onChange={(e) => setSearchFilters(prev => ({ ...prev, type: e.target.value }))}
                                className="filter-select"
                            >
                                <option value="all">All types</option>
                                <option value="text">Text only</option>
                                <option value="file">Files only</option>
                                <option value="image">Images only</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div className="search-results">
                    <div className="results-header">
                        <span className="results-count">
                            {isSearching ? 'Searching...' : `${searchResults.length} result${searchResults.length !== 1 ? 's' : ''}`}
                        </span>
                    </div>

                    <div className="results-list">
                        {searchResults.map((message, index) => (
                            <div key={message.id || index} className="search-result-item">
                                <div className="result-header">
                                    <span className="result-user">{message.user}</span>
                                    <span className="result-timestamp">{message.timestamp}</span>
                                    {message.room && <span className="result-room">[{message.room}]</span>}
                                    {message.private && <span className="result-private">Private</span>}
                                </div>
                                <div className="result-content">
                                    {message.type === 'text' ? (
                                        highlightText(message.message, searchQuery)
                                    ) : (
                                        <div className="result-file">
                                            📎 {highlightText(message.fileName || 'File', searchQuery)}
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}

                        {searchResults.length === 0 && (searchQuery || Object.values(searchFilters).some(f => f && f !== 'all')) && (
                            <div className="no-results">
                                No messages found matching your search criteria.
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default MessageSearch;
