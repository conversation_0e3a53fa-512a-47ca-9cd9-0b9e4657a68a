// components/MessagePagination.js
import React, { useState, useEffect, useCallback } from 'react';

/**
 * MessagePagination Component
 * Handles loading older messages with pagination and infinite scroll
 * 
 * @param {Object} props - Component props
 * @param {Object} props.socket - Socket.io client instance
 * @param {Array} props.messages - Current messages array
 * @param {Function} props.onMessagesLoaded - Callback when older messages are loaded
 * @param {boolean} props.enabled - Whether pagination is enabled
 */
function MessagePagination({ socket, messages, onMessagesLoaded, enabled = true }) {
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [loadedCount, setLoadedCount] = useState(0);
    const [error, setError] = useState(null);

    // Load older messages
    const loadOlderMessages = useCallback(async () => {
        if (!enabled || isLoading || !hasMore || !socket) return;

        setIsLoading(true);
        setError(null);

        try {
            // Request message history from server
            socket.emit('get_message_history', {
                offset: loadedCount,
                limit: 50
            });

            // Wait for response
            const timeout = setTimeout(() => {
                setError('Request timeout');
                setIsLoading(false);
            }, 10000);

            const handleMessageHistory = (data) => {
                clearTimeout(timeout);
                setIsLoading(false);
                
                if (data.messages && data.messages.length > 0) {
                    setLoadedCount(prev => prev + data.messages.length);
                    setHasMore(data.hasMore);
                    onMessagesLoaded && onMessagesLoaded(data.messages);
                } else {
                    setHasMore(false);
                }
                
                socket.off('message_history', handleMessageHistory);
            };

            socket.on('message_history', handleMessageHistory);

        } catch (err) {
            setError('Failed to load messages');
            setIsLoading(false);
        }
    }, [socket, enabled, isLoading, hasMore, loadedCount, onMessagesLoaded]);

    // Auto-load on scroll to top
    useEffect(() => {
        if (!enabled) return;

        const chatWindow = document.querySelector('.chat-window');
        if (!chatWindow) return;

        const handleScroll = () => {
            if (chatWindow.scrollTop === 0 && hasMore && !isLoading) {
                loadOlderMessages();
            }
        };

        chatWindow.addEventListener('scroll', handleScroll);
        return () => chatWindow.removeEventListener('scroll', handleScroll);
    }, [enabled, hasMore, isLoading, loadOlderMessages]);

    // Reset pagination when messages are cleared
    useEffect(() => {
        if (messages.length === 0) {
            setLoadedCount(0);
            setHasMore(true);
            setError(null);
        }
    }, [messages.length]);

    if (!enabled) return null;

    return (
        <div className="message-pagination">
            {/* Load more button (shown at top of chat) */}
            {hasMore && (
                <div className="load-more-container">
                    <button
                        className="load-more-btn"
                        onClick={loadOlderMessages}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <div className="loading-spinner">
                                <span>Loading older messages...</span>
                            </div>
                        ) : (
                            'Load older messages'
                        )}
                    </button>
                </div>
            )}

            {/* Error message */}
            {error && (
                <div className="pagination-error">
                    <span className="error-text">{error}</span>
                    <button 
                        className="retry-btn"
                        onClick={() => {
                            setError(null);
                            loadOlderMessages();
                        }}
                    >
                        Retry
                    </button>
                </div>
            )}

            {/* End of messages indicator */}
            {!hasMore && loadedCount > 0 && (
                <div className="end-of-messages">
                    <span>📜 Beginning of conversation</span>
                </div>
            )}

            {/* Scroll to top hint */}
            {hasMore && !isLoading && (
                <div className="scroll-hint">
                    <span>💡 Scroll to top to load older messages</span>
                </div>
            )}
        </div>
    );
}

export default MessagePagination;
