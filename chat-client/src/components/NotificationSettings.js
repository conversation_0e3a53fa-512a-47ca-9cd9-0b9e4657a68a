// components/NotificationSettings.js
import React, { useState, useEffect } from 'react';
import notificationService from '../services/NotificationService';
import soundGenerator from '../services/SoundGenerator';

/**
 * NotificationSettings Component
 * Provides controls for notification preferences and permissions
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the settings panel is open
 * @param {Function} props.onClose - Callback to close the settings panel
 */
function NotificationSettings({ isOpen, onClose }) {
    const [permissionStatus, setPermissionStatus] = useState(notificationService.getPermissionStatus());
    const [soundEnabled, setSoundEnabled] = useState(true);
    const [soundVolume, setSoundVolume] = useState(0.5);
    const [browserNotifications, setBrowserNotifications] = useState(true);

    useEffect(() => {
        // Load settings from localStorage
        const savedSettings = localStorage.getItem('chatNotificationSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            setSoundEnabled(settings.soundEnabled ?? true);
            setSoundVolume(settings.soundVolume ?? 0.5);
            setBrowserNotifications(settings.browserNotifications ?? true);
            
            // Apply settings
            notificationService.setSoundsEnabled(settings.soundEnabled ?? true);
            notificationService.setSoundVolume(settings.soundVolume ?? 0.5);
        }
    }, []);

    // Save settings to localStorage
    const saveSettings = (newSettings) => {
        const settings = {
            soundEnabled,
            soundVolume,
            browserNotifications,
            ...newSettings
        };
        
        localStorage.setItem('chatNotificationSettings', JSON.stringify(settings));
    };

    // Request notification permission
    const handleRequestPermission = async () => {
        const permission = await notificationService.requestPermission();
        setPermissionStatus(permission);
        
        if (permission === 'granted') {
            notificationService.showNotification('Notifications Enabled', {
                body: 'You will now receive chat notifications',
                tag: 'permission-granted'
            });
        }
    };

    // Toggle sound notifications
    const handleSoundToggle = (enabled) => {
        setSoundEnabled(enabled);
        notificationService.setSoundsEnabled(enabled);
        saveSettings({ soundEnabled: enabled });
        
        if (enabled) {
            soundGenerator.resumeContext();
            soundGenerator.playMessageSound(); // Test sound
        }
    };

    // Change sound volume
    const handleVolumeChange = (volume) => {
        setSoundVolume(volume);
        notificationService.setSoundVolume(volume);
        saveSettings({ soundVolume: volume });
        
        // Play test sound
        if (soundEnabled) {
            soundGenerator.playMessageSound();
        }
    };

    // Toggle browser notifications
    const handleBrowserNotificationsToggle = (enabled) => {
        setBrowserNotifications(enabled);
        saveSettings({ browserNotifications: enabled });
        
        if (enabled && permissionStatus !== 'granted') {
            handleRequestPermission();
        }
    };

    // Test notification
    const handleTestNotification = () => {
        if (browserNotifications && permissionStatus === 'granted') {
            notificationService.showNotification('Test Notification', {
                body: 'This is a test notification from the chat app',
                tag: 'test-notification'
            });
        }
        
        if (soundEnabled) {
            soundGenerator.playMessageSound();
        }
    };

    if (!isOpen) return null;

    return (
        <div className="notification-settings-overlay">
            <div className="notification-settings-panel">
                <div className="settings-header">
                    <h3>Notification Settings</h3>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>

                <div className="settings-content">
                    {/* Browser Notifications */}
                    <div className="setting-group">
                        <h4>Browser Notifications</h4>
                        <div className="setting-item">
                            <label className="setting-label">
                                <input
                                    type="checkbox"
                                    checked={browserNotifications}
                                    onChange={(e) => handleBrowserNotificationsToggle(e.target.checked)}
                                />
                                Enable browser notifications
                            </label>
                        </div>
                        
                        <div className="permission-status">
                            <span className={`status-indicator ${permissionStatus}`}>
                                {permissionStatus === 'granted' && '✅ Granted'}
                                {permissionStatus === 'denied' && '❌ Denied'}
                                {permissionStatus === 'default' && '⏳ Not requested'}
                            </span>
                            
                            {permissionStatus !== 'granted' && (
                                <button 
                                    className="request-permission-btn"
                                    onClick={handleRequestPermission}
                                >
                                    Request Permission
                                </button>
                            )}
                        </div>
                    </div>

                    {/* Sound Notifications */}
                    <div className="setting-group">
                        <h4>Sound Notifications</h4>
                        <div className="setting-item">
                            <label className="setting-label">
                                <input
                                    type="checkbox"
                                    checked={soundEnabled}
                                    onChange={(e) => handleSoundToggle(e.target.checked)}
                                />
                                Enable sound notifications
                            </label>
                        </div>
                        
                        {soundEnabled && (
                            <div className="setting-item">
                                <label className="setting-label">
                                    Volume: {Math.round(soundVolume * 100)}%
                                    <input
                                        type="range"
                                        min="0"
                                        max="1"
                                        step="0.1"
                                        value={soundVolume}
                                        onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                                        className="volume-slider"
                                    />
                                </label>
                            </div>
                        )}
                    </div>

                    {/* Test Notifications */}
                    <div className="setting-group">
                        <h4>Test Notifications</h4>
                        <button 
                            className="test-notification-btn"
                            onClick={handleTestNotification}
                        >
                            Test Notification
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default NotificationSettings;
