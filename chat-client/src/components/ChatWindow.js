// components/ChatWindow.js
import React, { useEffect, useRef } from 'react';
import MessageReactions from './MessageReactions';
import ReadReceipts from './ReadReceipts';

/**
 * ChatWindow Component
 * Enhanced chat window with support for reactions, read receipts, and file messages
 *
 * @param {Object} props - Component props
 * @param {Array} props.messages - Array of message objects
 * @param {string} props.typingUser - Currently typing user indicator
 * @param {string} props.username - Current user's username
 * @param {Function} props.onAddReaction - Callback for adding reactions
 * @param {Function} props.onMarkMessageRead - Callback for marking messages as read
 */
function ChatWindow({ messages, typingUser, username, onAddReaction, onMarkMessageRead }) {
    const chatEndRef = useRef(null);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    // Mark messages as read when they come into view
    useEffect(() => {
        messages.forEach(msg => {
            if (msg.user !== username && msg.id && onMarkMessageRead) {
                onMarkMessageRead(msg.id);
            }
        });
    }, [messages, username, onMarkMessageRead]);

    // Render file message content
    const renderFileMessage = (msg) => {
        if (msg.type === 'image') {
            return (
                <div className="file-message image-message">
                    <img
                        src={msg.fileUrl}
                        alt={msg.fileName}
                        className="chat-image"
                        onClick={() => window.open(msg.fileUrl, '_blank')}
                    />
                    <p className="file-name">{msg.fileName}</p>
                </div>
            );
        } else if (msg.type === 'video') {
            return (
                <div className="file-message video-message">
                    <video controls className="chat-video">
                        <source src={msg.fileUrl} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                    <p className="file-name">{msg.fileName}</p>
                </div>
            );
        } else if (msg.type === 'audio') {
            return (
                <div className="file-message audio-message">
                    <audio controls className="chat-audio">
                        <source src={msg.fileUrl} type="audio/mpeg" />
                        Your browser does not support the audio tag.
                    </audio>
                    <p className="file-name">{msg.fileName}</p>
                </div>
            );
        } else {
            return (
                <div className="file-message document-message">
                    <div className="file-icon">📄</div>
                    <div className="file-info">
                        <a
                            href={msg.fileUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="file-link"
                        >
                            {msg.fileName}
                        </a>
                        <span className="file-type">{msg.type}</span>
                    </div>
                </div>
            );
        }
    };

    return (
        <div className="chat-window">
            {messages.map(msg => (
                <div
                    className={`msg${msg.private ? " private" : msg.room ? " room" : ""} ${msg.type !== 'text' ? ' file-msg' : ''}`}
                    key={msg.id || msg.timestamp}
                >
                    <div className="message-header">
                        <strong className="message-sender">
                            {msg.private
                                ? `${msg.user} ➔ ${msg.to} (private)`
                                : msg.room
                                ? `[${msg.room}] ${msg.user}`
                                : msg.user}
                            :
                        </strong>
                        <span className="timestamp">({msg.timestamp})</span>
                    </div>

                    <div className="message-content">
                        {msg.type === 'text' ? (
                            <span className="message-text">{msg.message}</span>
                        ) : (
                            renderFileMessage(msg)
                        )}
                    </div>

                    <div className="message-footer">
                        {/* Message Reactions */}
                        {onAddReaction && (
                            <MessageReactions
                                reactions={msg.reactions}
                                messageId={msg.id}
                                onAddReaction={onAddReaction}
                                currentUser={username}
                            />
                        )}

                        {/* Read Receipts */}
                        <ReadReceipts
                            readBy={msg.readBy}
                            currentUser={username}
                            messageUser={msg.user}
                            isPrivate={msg.private}
                        />
                    </div>
                </div>
            ))}

            {typingUser && <div className="typing">{typingUser}</div>}
            <div ref={chatEndRef} />
        </div>
    );
}

export default ChatWindow;