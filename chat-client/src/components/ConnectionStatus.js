// components/ConnectionStatus.js
import React, { useState, useEffect } from 'react';

/**
 * ConnectionStatus Component
 * Displays connection status and handles reconnection logic
 * 
 * @param {Object} props - Component props
 * @param {Object} props.socket - Socket.io client instance
 * @param {Function} props.onReconnect - Callback when reconnection is successful
 */
function ConnectionStatus({ socket, onReconnect }) {
    const [connectionStatus, setConnectionStatus] = useState('connected');
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const [isReconnecting, setIsReconnecting] = useState(false);
    const [lastDisconnectTime, setLastDisconnectTime] = useState(null);

    useEffect(() => {
        if (!socket) return;

        // Connection event handlers
        const handleConnect = () => {
            setConnectionStatus('connected');
            setReconnectAttempts(0);
            setIsReconnecting(false);
            setLastDisconnectTime(null);
            onReconnect && onReconnect();
        };

        const handleDisconnect = (reason) => {
            setConnectionStatus('disconnected');
            setLastDisconnectTime(new Date());
            console.log('Disconnected:', reason);
            
            // Don't auto-reconnect if disconnection was intentional
            if (reason === 'io server disconnect') {
                setConnectionStatus('server_disconnect');
            }
        };

        const handleConnectError = (error) => {
            setConnectionStatus('error');
            console.error('Connection error:', error);
        };

        const handleReconnect = (attemptNumber) => {
            setReconnectAttempts(attemptNumber);
            setIsReconnecting(true);
            console.log(`Reconnection attempt ${attemptNumber}`);
        };

        const handleReconnectError = (error) => {
            console.error('Reconnection error:', error);
        };

        const handleReconnectFailed = () => {
            setConnectionStatus('failed');
            setIsReconnecting(false);
            console.error('Reconnection failed after maximum attempts');
        };

        // Register event listeners
        socket.on('connect', handleConnect);
        socket.on('disconnect', handleDisconnect);
        socket.on('connect_error', handleConnectError);
        socket.on('reconnect', handleReconnect);
        socket.on('reconnect_error', handleReconnectError);
        socket.on('reconnect_failed', handleReconnectFailed);

        // Set initial status
        setConnectionStatus(socket.connected ? 'connected' : 'disconnected');

        // Cleanup
        return () => {
            socket.off('connect', handleConnect);
            socket.off('disconnect', handleDisconnect);
            socket.off('connect_error', handleConnectError);
            socket.off('reconnect', handleReconnect);
            socket.off('reconnect_error', handleReconnectError);
            socket.off('reconnect_failed', handleReconnectFailed);
        };
    }, [socket, onReconnect]);

    // Manual reconnection
    const handleManualReconnect = () => {
        if (socket && !socket.connected) {
            setIsReconnecting(true);
            socket.connect();
        }
    };

    // Get status display info
    const getStatusInfo = () => {
        switch (connectionStatus) {
            case 'connected':
                return {
                    text: 'Connected',
                    icon: '🟢',
                    className: 'status-connected',
                    showReconnect: false
                };
            case 'disconnected':
                return {
                    text: isReconnecting ? `Reconnecting... (${reconnectAttempts})` : 'Disconnected',
                    icon: isReconnecting ? '🟡' : '🔴',
                    className: isReconnecting ? 'status-reconnecting' : 'status-disconnected',
                    showReconnect: !isReconnecting
                };
            case 'server_disconnect':
                return {
                    text: 'Server disconnected',
                    icon: '🔴',
                    className: 'status-server-disconnect',
                    showReconnect: true
                };
            case 'error':
                return {
                    text: 'Connection error',
                    icon: '❌',
                    className: 'status-error',
                    showReconnect: true
                };
            case 'failed':
                return {
                    text: 'Connection failed',
                    icon: '❌',
                    className: 'status-failed',
                    showReconnect: true
                };
            default:
                return {
                    text: 'Unknown status',
                    icon: '❓',
                    className: 'status-unknown',
                    showReconnect: true
                };
        }
    };

    const statusInfo = getStatusInfo();

    // Don't show status when connected (unless there were recent issues)
    if (connectionStatus === 'connected' && reconnectAttempts === 0) {
        return null;
    }

    return (
        <div className={`connection-status ${statusInfo.className}`}>
            <div className="status-content">
                <span className="status-icon">{statusInfo.icon}</span>
                <span className="status-text">{statusInfo.text}</span>
                
                {lastDisconnectTime && (
                    <span className="disconnect-time">
                        (since {lastDisconnectTime.toLocaleTimeString()})
                    </span>
                )}
            </div>

            {statusInfo.showReconnect && (
                <button 
                    className="reconnect-btn"
                    onClick={handleManualReconnect}
                    disabled={isReconnecting}
                >
                    {isReconnecting ? 'Reconnecting...' : 'Reconnect'}
                </button>
            )}

            {connectionStatus === 'disconnected' && !isReconnecting && (
                <div className="connection-tips">
                    <p>Check your internet connection and try reconnecting.</p>
                </div>
            )}
        </div>
    );
}

export default ConnectionStatus;
