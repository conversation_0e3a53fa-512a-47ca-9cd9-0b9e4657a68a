// components/__tests__/MessageInput.test.js
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import MessageInput from '../MessageInput';

// Mock the FileUpload component
jest.mock('../FileUpload', () => {
    return function MockFileUpload({ onFileUpload, onUploadStart, onUploadError }) {
        return (
            <div data-testid="file-upload">
                <button 
                    onClick={() => {
                        onUploadStart();
                        setTimeout(() => {
                            onFileUpload({
                                fileName: 'test.jpg',
                                fileUrl: 'http://localhost:5000/uploads/test.jpg',
                                type: 'image'
                            });
                        }, 100);
                    }}
                >
                    Upload File
                </button>
                <button onClick={() => onUploadError('Upload failed')}>
                    Simulate Error
                </button>
            </div>
        );
    };
});

describe('MessageInput Component', () => {
    const mockSocket = {
        emit: jest.fn()
    };

    const defaultProps = {
        message: '',
        setMessage: jest.fn(),
        socket: mockSocket,
        recipient: '',
        currentRoom: 'global'
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('renders input field and send button', () => {
        render(<MessageInput {...defaultProps} />);
        
        expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Send' })).toBeInTheDocument();
    });

    test('calls setMessage when typing', () => {
        render(<MessageInput {...defaultProps} />);
        
        const input = screen.getByPlaceholderText('Type a message...');
        fireEvent.change(input, { target: { value: 'Hello world' } });
        
        expect(defaultProps.setMessage).toHaveBeenCalledWith('Hello world');
    });

    test('emits typing event when user types', () => {
        render(<MessageInput {...defaultProps} />);
        
        const input = screen.getByPlaceholderText('Type a message...');
        fireEvent.change(input, { target: { value: 'Hello' } });
        
        expect(mockSocket.emit).toHaveBeenCalledWith('typing', {
            isTyping: true,
            to: '',
            room: 'global'
        });
    });

    test('sends message on form submit', () => {
        render(<MessageInput {...defaultProps} message="Hello world" />);

        const form = document.querySelector('.message-input-form');
        fireEvent.submit(form);
        
        expect(mockSocket.emit).toHaveBeenCalledWith('send_message', {
            message: 'Hello world',
            type: 'text'
        });
    });

    test('sends private message when recipient is set', () => {
        render(<MessageInput {...defaultProps} message="Private message" recipient="Alice" />);

        const form = document.querySelector('.message-input-form');
        fireEvent.submit(form);

        expect(mockSocket.emit).toHaveBeenCalledWith('send_message', {
            message: 'Private message',
            type: 'text',
            to: 'Alice'
        });
    });

    test('sends room message when in specific room', () => {
        render(<MessageInput {...defaultProps} message="Room message" currentRoom="general" />);

        const form = document.querySelector('.message-input-form');
        fireEvent.submit(form);

        expect(mockSocket.emit).toHaveBeenCalledWith('send_message', {
            message: 'Room message',
            type: 'text',
            room: 'general'
        });
    });

    test('prevents sending empty messages', () => {
        render(<MessageInput {...defaultProps} message="" />);

        const form = document.querySelector('.message-input-form');
        fireEvent.submit(form);

        expect(mockSocket.emit).not.toHaveBeenCalledWith('send_message', expect.any(Object));
    });

    test('disables input and button during file upload', async () => {
        render(<MessageInput {...defaultProps} />);
        
        const uploadButton = screen.getByText('Upload File');
        fireEvent.click(uploadButton);
        
        await waitFor(() => {
            expect(screen.getByPlaceholderText('Uploading file...')).toBeDisabled();
            expect(screen.getByRole('button', { name: '⏳' })).toBeDisabled();
        });
    });

    test('handles file upload successfully', async () => {
        render(<MessageInput {...defaultProps} />);
        
        const uploadButton = screen.getByText('Upload File');
        fireEvent.click(uploadButton);
        
        await waitFor(() => {
            expect(mockSocket.emit).toHaveBeenCalledWith('send_message', {
                message: 'Shared a file: test.jpg',
                type: 'image',
                fileName: 'test.jpg',
                fileUrl: 'http://localhost:5000/uploads/test.jpg'
            });
        });
    });

    test('handles file upload error', async () => {
        // Mock alert
        window.alert = jest.fn();
        
        render(<MessageInput {...defaultProps} />);
        
        const errorButton = screen.getByText('Simulate Error');
        fireEvent.click(errorButton);
        
        await waitFor(() => {
            expect(window.alert).toHaveBeenCalledWith('Upload failed: Upload failed');
        });
    });

    test('emits stop typing on message send', () => {
        render(<MessageInput {...defaultProps} message="Hello" />);

        const form = document.querySelector('.message-input-form');
        fireEvent.submit(form);
        
        expect(mockSocket.emit).toHaveBeenCalledWith('typing', {
            isTyping: false,
            to: '',
            room: 'global'
        });
    });

    test('renders file upload component', () => {
        render(<MessageInput {...defaultProps} />);
        
        expect(screen.getByTestId('file-upload')).toBeInTheDocument();
    });
});
