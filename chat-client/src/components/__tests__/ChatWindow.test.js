// components/__tests__/ChatWindow.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ChatWindow from '../ChatWindow';

// Mock the child components
jest.mock('../MessageReactions', () => {
    return function MockMessageReactions({ messageId, onAddReaction }) {
        return (
            <div data-testid="message-reactions">
                <button onClick={() => onAddReaction(messageId, 'like')}>
                    Add Like
                </button>
            </div>
        );
    };
});

jest.mock('../ReadReceipts', () => {
    return function MockReadReceipts({ readBy }) {
        return <div data-testid="read-receipts">{readBy?.length || 0} read</div>;
    };
});

// Mock scrollIntoView
Object.defineProperty(Element.prototype, 'scrollIntoView', {
    value: jest.fn(),
    writable: true
});

describe('ChatWindow Component', () => {
    const mockMessages = [
        {
            id: 1,
            user: 'Alice',
            message: 'Hello everyone!',
            timestamp: '10:30:00 AM',
            type: 'text',
            reactions: { like: ['Bob'] },
            readBy: ['Alice', 'Bob']
        },
        {
            id: 2,
            user: 'Bob',
            message: 'Hi Alice!',
            timestamp: '10:31:00 AM',
            type: 'text',
            private: true,
            to: 'Alice',
            reactions: {},
            readBy: ['Bob']
        },
        {
            id: 3,
            user: 'Charlie',
            message: 'Shared a file: document.pdf',
            timestamp: '10:32:00 AM',
            type: 'file',
            fileName: 'document.pdf',
            fileUrl: 'http://localhost:5000/uploads/document.pdf',
            reactions: {},
            readBy: ['Charlie']
        }
    ];

    const defaultProps = {
        messages: mockMessages,
        typingUser: '',
        username: 'TestUser',
        onAddReaction: jest.fn(),
        onMarkMessageRead: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('renders messages correctly', () => {
        render(<ChatWindow {...defaultProps} />);
        
        expect(screen.getByText('Alice:')).toBeInTheDocument();
        expect(screen.getByText('Hello everyone!')).toBeInTheDocument();
        expect(screen.getByText('Bob ➔ Alice (private):')).toBeInTheDocument();
        expect(screen.getByText('Hi Alice!')).toBeInTheDocument();
    });

    test('displays typing indicator when provided', () => {
        render(<ChatWindow {...defaultProps} typingUser="Alice is typing..." />);
        
        expect(screen.getByText('Alice is typing...')).toBeInTheDocument();
    });

    test('renders file messages correctly', () => {
        render(<ChatWindow {...defaultProps} />);
        
        expect(screen.getByText('document.pdf')).toBeInTheDocument();
        expect(screen.getByRole('link')).toHaveAttribute('href', 'http://localhost:5000/uploads/document.pdf');
    });

    test('calls onAddReaction when reaction is added', () => {
        render(<ChatWindow {...defaultProps} />);
        
        const reactionButtons = screen.getAllByText('Add Like');
        fireEvent.click(reactionButtons[0]);
        
        expect(defaultProps.onAddReaction).toHaveBeenCalledWith(1, 'like');
    });

    test('applies correct CSS classes for message types', () => {
        render(<ChatWindow {...defaultProps} />);
        
        const messages = screen.getAllByRole('generic').filter(el => 
            el.className.includes('msg')
        );
        
        expect(messages[1]).toHaveClass('private');
        expect(messages[2]).toHaveClass('file-msg');
    });

    test('renders message reactions and read receipts', () => {
        render(<ChatWindow {...defaultProps} />);
        
        expect(screen.getAllByTestId('message-reactions')).toHaveLength(3);
        expect(screen.getAllByTestId('read-receipts')).toHaveLength(3);
    });

    test('handles empty messages array', () => {
        render(<ChatWindow {...defaultProps} messages={[]} />);
        
        expect(screen.queryByText('Alice:')).not.toBeInTheDocument();
    });

    test('renders image messages correctly', () => {
        const imageMessage = {
            id: 4,
            user: 'Dave',
            message: 'Shared an image: photo.jpg',
            timestamp: '10:33:00 AM',
            type: 'image',
            fileName: 'photo.jpg',
            fileUrl: 'http://localhost:5000/uploads/photo.jpg',
            reactions: {},
            readBy: ['Dave']
        };

        render(<ChatWindow {...defaultProps} messages={[imageMessage]} />);
        
        const image = screen.getByRole('img');
        expect(image).toHaveAttribute('src', 'http://localhost:5000/uploads/photo.jpg');
        expect(image).toHaveAttribute('alt', 'photo.jpg');
    });
});
