// components/ReadReceipts.js
import React from 'react';

/**
 * ReadReceipts Component
 * Displays read receipts for messages showing who has read them
 * 
 * @param {Object} props - Component props
 * @param {Array} props.readBy - Array of usernames who have read the message
 * @param {string} props.currentUser - Current user's username
 * @param {string} props.messageUser - Username of the message sender
 * @param {boolean} props.isPrivate - Whether this is a private message
 */
function ReadReceipts({ readBy = [], currentUser, messageUser, isPrivate }) {
    // Don't show read receipts for the sender's own messages in public chat
    if (!isPrivate && messageUser === currentUser) {
        return null;
    }

    // Filter out the message sender from read receipts
    const readers = readBy.filter(user => user !== messageUser);

    // Don't show if no one has read it yet (besides sender)
    if (readers.length === 0) {
        return null;
    }

    // For private messages, show more detailed read receipts
    if (isPrivate) {
        const hasCurrentUserRead = readers.includes(currentUser);
        const otherReaders = readers.filter(user => user !== currentUser);

        return (
            <div className="read-receipts private">
                <div className="read-status">
                    {hasCurrentUserRead && messageUser !== currentUser && (
                        <span className="read-indicator you-read" title="You have read this message">
                            ✓✓
                        </span>
                    )}
                    {otherReaders.length > 0 && messageUser === currentUser && (
                        <span className="read-indicator others-read" title={`Read by: ${otherReaders.join(', ')}`}>
                            ✓✓
                        </span>
                    )}
                </div>
            </div>
        );
    }

    // For public messages, show a simple read count
    return (
        <div className="read-receipts public">
            <span className="read-count" title={`Read by: ${readers.join(', ')}`}>
                👁️ {readers.length}
            </span>
        </div>
    );
}

export default ReadReceipts;
