/* General background and layout */
body {
  background: linear-gradient(135deg, #e0e7ff 0%, #bae6fd 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', '<PERSON><PERSON>', <PERSON>l, sans-serif;
  margin: 0;
  padding: 0;
}

/* App styles */
.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Main chat card */
.chat-container {
  background: rgba(255,255,255,0.95);
  max-width: 600px;
  margin: 40px auto;
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(30, 64, 175, 0.15);
  padding: 32px 24px;
  border: 1.5px solid #93c5fd;
}

/* Headings */
h2, h3 {
  color: #2563eb;
  margin-bottom: 12px;
  font-weight: 700;
}

/* Room joiner form */
.room-joiner-form {
  display: flex;
  gap: 10px;
  margin-bottom: 18px;
  background: #eff6ff;
  padding: 14px;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.06);
}

.room-joiner-input {
  flex: 1;
  padding: 8px 12px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #fff;
  color: #1e3a8a;
  font-size: 1rem;
  transition: border 0.2s;
}

.room-joiner-input:focus {
  border-color: #2563eb;
  outline: none;
}

.room-joiner-btn {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.10);
}

.room-joiner-btn:hover {
  background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
}

/* User list */
.user-list {
  background: #eff6ff;
  padding: 14px;
  border-radius: 10px;
  margin-bottom: 18px;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.06);
}

.user-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-list li {
  padding: 6px 0;
  cursor: pointer;
  color: #1e40af;
  border-radius: 6px;
  transition: background 0.15s, color 0.15s;
}

.user-list li.selected,
.user-list li:hover {
  background: #dbeafe;
  color: #2563eb;
  font-weight: 600;
}

.user-list .you {
  color: #60a5fa;
  font-weight: 500;
}

.user-list .everyone-btn {
  margin-top: 8px;
  color: #2563eb;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
  text-decoration: underline;
}

.user-list .recipient-label {
  margin-top: 4px;
  color: #2563eb;
  font-size: 0.95rem;
}

/* Chat window */
.chat-window {
  background: #f0f9ff;
  border-radius: 10px;
  padding: 16px;
  height: 260px;
  overflow-y: auto;
  margin-bottom: 18px;
  border: 1.5px solid #bae6fd;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.06);
}

.chat-window .msg {
  margin-bottom: 10px;
  color: #1e3a8a;
  font-size: 1rem;
}

.chat-window .msg.private {
  color: #7c3aed;
}

.chat-window .msg.room {
  color: #059669;
}

.chat-window .msg .timestamp {
  font-size: 0.85em;
  color: #60a5fa;
  margin-left: 6px;
}

.chat-window .typing {
  color: #2563eb;
  font-style: italic;
  margin-top: 8px;
}

/* Message input */
.message-input-container {
  width: 100%;
}

.message-input-form {
  display: flex;
  gap: 10px;
}

.input-group {
  display: flex;
  gap: 8px;
  width: 100%;
  align-items: center;
}

.message-input-box {
  flex: 1;
  padding: 10px 14px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #fff;
  color: #1e3a8a;
  font-size: 1rem;
  transition: border 0.2s;
}

.message-input-box:focus {
  border-color: #2563eb;
  outline: none;
}

.message-input-box:disabled {
  background: #f3f4f6;
  color: #6b7280;
}

.message-send-btn {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.10);
  min-width: 70px;
}

.message-send-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
}

.message-send-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Username form */
.username-form {
  background: #fff;
  padding: 32px 24px;
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(30, 64, 175, 0.15);
  border: 1.5px solid #93c5fd;
  max-width: 350px;
  margin: 60px auto 0 auto;
}

.username-form h2 {
  color: #2563eb;
  margin-bottom: 18px;
  font-weight: 700;
}

.username-form input {
  width: 100%;
  padding: 10px 14px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #f0f9ff;
  color: #1e3a8a;
  font-size: 1rem;
  margin-bottom: 18px;
  transition: border 0.2s;
}

.username-form input:focus {
  border-color: #2563eb;
  outline: none;
}

.username-form button {
  width: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 0;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.10);
}

.username-form button:hover {
  background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
}

/* Enhanced message styles */
.msg {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  border-left: 3px solid #3b82f6;
}

.msg.private {
  border-left-color: #7c3aed;
  background: rgba(124, 58, 237, 0.05);
}

.msg.room {
  border-left-color: #059669;
  background: rgba(5, 150, 105, 0.05);
}

.msg.file-msg {
  border-left-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-sender {
  color: #1e40af;
  font-size: 0.9rem;
}

.message-content {
  margin-bottom: 8px;
}

.message-text {
  color: #1e3a8a;
  line-height: 1.4;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

/* File message styles */
.file-message {
  max-width: 300px;
}

.file-message.image-message .chat-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.file-message.image-message .chat-image:hover {
  transform: scale(1.02);
}

.file-message.video-message .chat-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

.file-message.audio-message .chat-audio {
  width: 100%;
}

.file-message.document-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
}

.file-icon {
  font-size: 2rem;
}

.file-info {
  flex: 1;
}

.file-link {
  color: #2563eb;
  text-decoration: none;
  font-weight: 500;
}

.file-link:hover {
  text-decoration: underline;
}

.file-name {
  margin: 4px 0 0 0;
  font-size: 0.85rem;
  color: #6b7280;
}

.file-type {
  display: block;
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
}

/* File upload styles */
.file-upload-container {
  position: relative;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1.2rem;
}

.file-upload-btn:hover {
  background: #eff6ff;
  border-color: #2563eb;
}

.file-upload-btn.uploading {
  background: #fef3c7;
  border-color: #f59e0b;
  cursor: not-allowed;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.progress-text {
  font-size: 0.7rem;
  color: #92400e;
}

.upload-progress-bar {
  position: absolute;
  bottom: -6px;
  left: 0;
  right: 0;
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  transition: width 0.3s ease;
}

/* Message reactions styles */
.message-reactions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.existing-reactions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.reaction-badge {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  padding: 2px 6px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.reaction-badge:hover {
  background: #e5e7eb;
}

.reaction-badge.user-reaction {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1e40af;
}

.reaction-picker {
  position: relative;
  display: inline-block;
}

.add-reaction-btn {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.add-reaction-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.reaction-options {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 4px;
  display: none;
  gap: 2px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.reaction-picker:hover .reaction-options {
  display: flex;
}

.reaction-option {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}

.reaction-option:hover {
  background: #f3f4f6;
}

.reaction-option.selected {
  background: #dbeafe;
}

/* Read receipts styles */
.read-receipts {
  display: flex;
  align-items: center;
  gap: 4px;
}

.read-receipts.private .read-indicator {
  color: #059669;
  font-size: 0.8rem;
}

.read-receipts.private .read-indicator.you-read {
  color: #3b82f6;
}

.read-receipts.private .read-indicator.others-read {
  color: #059669;
}

.read-receipts.public .read-count {
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 2px;
}

/* Chat header styles */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-controls {
  display: flex;
  gap: 8px;
}

.notification-settings-btn,
.search-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-settings-btn:hover,
.search-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Notification settings modal */
.notification-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.notification-settings-panel {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
}

.settings-header h3 {
  margin: 0;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-group {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 1rem;
}

.setting-item {
  margin-bottom: 12px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;
  color: #4b5563;
}

.setting-label input[type="checkbox"] {
  margin-right: 8px;
}

.volume-slider {
  width: 100%;
  margin-top: 8px;
}

.permission-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.status-indicator {
  font-size: 0.9rem;
  padding: 4px 8px;
  border-radius: 4px;
}

.status-indicator.granted {
  background: #d1fae5;
  color: #065f46;
}

.status-indicator.denied {
  background: #fee2e2;
  color: #991b1b;
}

.status-indicator.default {
  background: #fef3c7;
  color: #92400e;
}

.request-permission-btn,
.test-notification-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.request-permission-btn:hover,
.test-notification-btn:hover {
  background: #2563eb;
}

/* Unread counter styles */
.unread-counter-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  margin-bottom: 12px;
}

.unread-badge {
  background: #ef4444;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.unread-badge.total-unread {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 24px;
  height: 24px;
  font-size: 0.8rem;
}

.unread-indicator {
  flex: 1;
}

.unread-text {
  color: #92400e;
  font-weight: 500;
  font-size: 0.9rem;
}

.unread-breakdown {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.unread-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
}

.unread-label {
  color: #6b7280;
}

.unread-count {
  background: #3b82f6;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .chat-container {
    margin: 20px auto;
    padding: 16px;
  }

  .chat-window {
    height: 200px;
  }

  .message-input-form {
    flex-direction: column;
    gap: 8px;
  }

  .input-group {
    flex-direction: row;
  }

  .file-message {
    max-width: 100%;
  }

  .file-message.image-message .chat-image,
  .file-message.video-message .chat-video {
    max-height: 150px;
  }

  .chat-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .notification-settings-panel {
    width: 95%;
    padding: 16px;
  }

  .unread-breakdown {
    flex-direction: column;
    gap: 8px;
  }
}

/* Message search styles */
.message-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.message-search-panel {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
}

.search-controls {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
  padding: 10px 14px;
  border: 1.5px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
}

.search-input:focus {
  border-color: #3b82f6;
  outline: none;
}

.clear-search-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.search-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.9rem;
  color: #4b5563;
  font-weight: 500;
}

.filter-input,
.filter-select {
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.search-results {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.results-header {
  margin-bottom: 12px;
}

.results-count {
  font-weight: 500;
  color: #4b5563;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.search-result-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  background: #f9fafb;
}

.result-header {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.result-user {
  font-weight: 600;
  color: #1f2937;
}

.result-timestamp {
  color: #6b7280;
}

.result-room {
  background: #dbeafe;
  color: #1e40af;
  padding: 2px 6px;
  border-radius: 4px;
}

.result-private {
  background: #fce7f3;
  color: #be185d;
  padding: 2px 6px;
  border-radius: 4px;
}

.result-content {
  color: #374151;
  line-height: 1.4;
}

.result-file {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #059669;
}

.search-highlight {
  background: #fef08a;
  padding: 1px 2px;
  border-radius: 2px;
}

.no-results {
  text-align: center;
  color: #6b7280;
  padding: 40px 20px;
  font-style: italic;
}

/* Connection status styles */
.connection-status {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-status.status-connected {
  background: #d1fae5;
  border-color: #10b981;
}

.connection-status.status-disconnected {
  background: #fee2e2;
  border-color: #ef4444;
}

.connection-status.status-reconnecting {
  background: #fef3c7;
  border-color: #f59e0b;
}

.connection-status.status-error,
.connection-status.status-failed {
  background: #fee2e2;
  border-color: #ef4444;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 1.2rem;
}

.status-text {
  font-weight: 500;
  color: #374151;
}

.disconnect-time {
  font-size: 0.8rem;
  color: #6b7280;
  margin-left: 8px;
}

.reconnect-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 0.9rem;
}

.reconnect-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.connection-tips {
  margin-top: 8px;
  font-size: 0.85rem;
  color: #6b7280;
}

/* Message pagination styles */
.message-pagination {
  margin-bottom: 12px;
}

.load-more-container {
  text-align: center;
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.load-more-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #374151;
  transition: all 0.2s;
}

.load-more-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.load-more-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.loading-spinner {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-error {
  background: #fee2e2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-text {
  color: #991b1b;
  font-size: 0.9rem;
}

.retry-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 0.8rem;
}

.end-of-messages {
  text-align: center;
  padding: 16px;
  color: #6b7280;
  font-size: 0.9rem;
  border-bottom: 1px solid #e5e7eb;
}

.scroll-hint {
  text-align: center;
  padding: 8px;
  color: #6b7280;
  font-size: 0.8rem;
  background: #f9fafb;
  border-radius: 6px;
  margin-bottom: 8px;
}

/* Search active indicator */
.search-active-indicator {
  background: #dbeafe;
  border: 1px solid #3b82f6;
  border-radius: 8px;
  padding: 8px 12px;
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #1e40af;
}

.search-active-indicator .clear-search-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 0.8rem;
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
  .chat-header {
    flex-direction: column;
    gap: 8px;
  }

  .header-controls {
    justify-content: center;
  }

  .message-search-panel {
    width: 95%;
    padding: 16px;
    max-height: 90vh;
  }

  .search-filters {
    grid-template-columns: 1fr;
  }

  .search-input-group {
    flex-direction: column;
  }

  .connection-status {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .search-active-indicator {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .notification-settings-panel {
    width: 95%;
    max-height: 90vh;
  }
}
