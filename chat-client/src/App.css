/* General background and layout */
body {
  background: linear-gradient(135deg, #e0e7ff 0%, #bae6fd 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', '<PERSON><PERSON>', <PERSON>l, sans-serif;
  margin: 0;
  padding: 0;
}

/* App styles */
.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Main chat card */
.chat-container {
  background: rgba(255,255,255,0.95);
  max-width: 600px;
  margin: 40px auto;
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(30, 64, 175, 0.15);
  padding: 32px 24px;
  border: 1.5px solid #93c5fd;
}

/* Headings */
h2, h3 {
  color: #2563eb;
  margin-bottom: 12px;
  font-weight: 700;
}

/* Room joiner form */
.room-joiner-form {
  display: flex;
  gap: 10px;
  margin-bottom: 18px;
  background: #eff6ff;
  padding: 14px;
  border-radius: 10px;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.06);
}

.room-joiner-input {
  flex: 1;
  padding: 8px 12px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #fff;
  color: #1e3a8a;
  font-size: 1rem;
  transition: border 0.2s;
}

.room-joiner-input:focus {
  border-color: #2563eb;
  outline: none;
}

.room-joiner-btn {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.10);
}

.room-joiner-btn:hover {
  background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
}

/* User list */
.user-list {
  background: #eff6ff;
  padding: 14px;
  border-radius: 10px;
  margin-bottom: 18px;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.06);
}

.user-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-list li {
  padding: 6px 0;
  cursor: pointer;
  color: #1e40af;
  border-radius: 6px;
  transition: background 0.15s, color 0.15s;
}

.user-list li.selected,
.user-list li:hover {
  background: #dbeafe;
  color: #2563eb;
  font-weight: 600;
}

.user-list .you {
  color: #60a5fa;
  font-weight: 500;
}

.user-list .everyone-btn {
  margin-top: 8px;
  color: #2563eb;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
  text-decoration: underline;
}

.user-list .recipient-label {
  margin-top: 4px;
  color: #2563eb;
  font-size: 0.95rem;
}

/* Chat window */
.chat-window {
  background: #f0f9ff;
  border-radius: 10px;
  padding: 16px;
  height: 260px;
  overflow-y: auto;
  margin-bottom: 18px;
  border: 1.5px solid #bae6fd;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.06);
}

.chat-window .msg {
  margin-bottom: 10px;
  color: #1e3a8a;
  font-size: 1rem;
}

.chat-window .msg.private {
  color: #7c3aed;
}

.chat-window .msg.room {
  color: #059669;
}

.chat-window .msg .timestamp {
  font-size: 0.85em;
  color: #60a5fa;
  margin-left: 6px;
}

.chat-window .typing {
  color: #2563eb;
  font-style: italic;
  margin-top: 8px;
}

/* Message input */
.message-input-form {
  display: flex;
  gap: 10px;
}

.message-input-box {
  flex: 1;
  padding: 10px 14px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #fff;
  color: #1e3a8a;
  font-size: 1rem;
  transition: border 0.2s;
}

.message-input-box:focus {
  border-color: #2563eb;
  outline: none;
}

.message-send-btn {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.10);
}

.message-send-btn:hover {
  background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
}

/* Username form */
.username-form {
  background: #fff;
  padding: 32px 24px;
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(30, 64, 175, 0.15);
  border: 1.5px solid #93c5fd;
  max-width: 350px;
  margin: 60px auto 0 auto;
}

.username-form h2 {
  color: #2563eb;
  margin-bottom: 18px;
  font-weight: 700;
}

.username-form input {
  width: 100%;
  padding: 10px 14px;
  border: 1.5px solid #93c5fd;
  border-radius: 8px;
  background: #f0f9ff;
  color: #1e3a8a;
  font-size: 1rem;
  margin-bottom: 18px;
  transition: border 0.2s;
}

.username-form input:focus {
  border-color: #2563eb;
  outline: none;
}

.username-form button {
  width: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 0;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.10);
}

.username-form button:hover {
  background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
}
