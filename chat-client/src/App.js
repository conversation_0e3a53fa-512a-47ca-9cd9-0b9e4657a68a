// Step 3 implementation: Improved UI with components without breaking Step 2 functionality
// Folder structure:
// src/
//   App.js
//   components/ChatWindow.js
//   components/MessageInput.js
//   components/UserList.js
//   components/RoomJoiner.js

// == App.js ==
import React, { useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import ChatWindow from './components/ChatWindow';
import MessageInput from './components/MessageInput';
import UserList from './components/UserList';
import RoomJoiner from './components/RoomJoiner';
import NotificationSettings from './components/NotificationSettings';
import UnreadCounter from './components/UnreadCounter';
import MessageSearch from './components/MessageSearch';
import ConnectionStatus from './components/ConnectionStatus';
import MessagePagination from './components/MessagePagination';
import notificationService from './services/NotificationService';
import soundGenerator from './services/SoundGenerator';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const socket = io('http://localhost:5000');

function App() {
    const [username, setUsername] = useState('');
    const [isUsernameSet, setIsUsernameSet] = useState(false);
    const [message, setMessage] = useState('');
    const [messages, setMessages] = useState([]);
    const [onlineUsers, setOnlineUsers] = useState([]);
    const [typingUser, setTypingUser] = useState('');
    const [recipient, setRecipient] = useState('');
    const [currentRoom, setCurrentRoom] = useState('global');
    const [newRoom, setNewRoom] = useState('');
    const [showNotificationSettings, setShowNotificationSettings] = useState(false);
    const [isWindowFocused, setIsWindowFocused] = useState(true);
    const [showMessageSearch, setShowMessageSearch] = useState(false);
    const [searchResults, setSearchResults] = useState([]);
    const [isSearchActive, setIsSearchActive] = useState(false);
    const [allMessages, setAllMessages] = useState([]); // Store all messages including paginated ones

    // Track window focus for notification management
    useEffect(() => {
        const handleFocus = () => setIsWindowFocused(true);
        const handleBlur = () => setIsWindowFocused(false);

        window.addEventListener('focus', handleFocus);
        window.addEventListener('blur', handleBlur);

        return () => {
            window.removeEventListener('focus', handleFocus);
            window.removeEventListener('blur', handleBlur);
        };
    }, []);

    // Initialize notification service
    useEffect(() => {
        // Resume audio context on first user interaction
        const handleFirstInteraction = () => {
            soundGenerator.resumeContext();
            document.removeEventListener('click', handleFirstInteraction);
            document.removeEventListener('keydown', handleFirstInteraction);
        };

        document.addEventListener('click', handleFirstInteraction);
        document.addEventListener('keydown', handleFirstInteraction);

        return () => {
            document.removeEventListener('click', handleFirstInteraction);
            document.removeEventListener('keydown', handleFirstInteraction);
        };
    }, []);

    useEffect(() => {
        socket.on('receive_message', (data) => {
            setMessages(prev => [...prev, data]);

            // Show notification if window is not focused or it's a private message/mention
            if (!isWindowFocused || data.private || data.message.toLowerCase().includes(`@${username.toLowerCase()}`)) {
                notificationService.notifyNewMessage(data, username);
            }
        });

        socket.on('online_users', (users) => {
            setOnlineUsers(users);
        });

        socket.on('user_typing', ({ user, isTyping }) => {
            setTypingUser(isTyping ? `${user} is typing...` : '');
        });

        socket.on('user_joined', (user) => {
            toast.info(`${user} joined the chat.`);
            if (!isWindowFocused) {
                notificationService.notifyUserJoined(user);
            }
        });

        socket.on('user_left', (user) => {
            toast.info(`${user} left the chat.`);
            if (!isWindowFocused) {
                notificationService.notifyUserLeft(user);
            }
        });

        // Handle reaction updates
        socket.on('reaction_updated', ({ messageId, reactions }) => {
            setMessages(prev => prev.map(msg =>
                msg.id === messageId ? { ...msg, reactions } : msg
            ));
        });

        // Handle read receipts
        socket.on('message_read', ({ messageId, readBy }) => {
            setMessages(prev => prev.map(msg => {
                if (msg.id === messageId) {
                    const updatedReadBy = [...(msg.readBy || [])];
                    if (!updatedReadBy.includes(readBy)) {
                        updatedReadBy.push(readBy);
                    }
                    return { ...msg, readBy: updatedReadBy };
                }
                return msg;
            }));
        });

        return () => {
            socket.off('receive_message');
            socket.off('online_users');
            socket.off('user_typing');
            socket.off('user_joined');
            socket.off('user_left');
            socket.off('reaction_updated');
            socket.off('message_read');
        };
    }, []);

    const handleUsernameSubmit = (e) => {
        e.preventDefault();
        if (username.trim() !== '') {
            socket.emit('set_username', username);
            setIsUsernameSet(true);
        }
    };

    // Handle adding reactions to messages
    const handleAddReaction = (messageId, reaction) => {
        socket.emit('add_reaction', { messageId, reaction });
    };

    // Handle marking messages as read
    const handleMarkMessageRead = (messageId) => {
        socket.emit('mark_message_read', { messageId });
    };

    // Handle search results
    const handleSearchResults = (results) => {
        setSearchResults(results);
        setIsSearchActive(results.length > 0);
    };

    // Handle loading older messages
    const handleOlderMessagesLoaded = (olderMessages) => {
        setAllMessages(prev => [...olderMessages, ...prev]);
        setMessages(prev => [...olderMessages, ...prev]);
    };

    // Handle reconnection
    const handleReconnection = () => {
        // Re-emit username to re-establish session
        if (username) {
            socket.emit('set_username', username);
        }
        // Rejoin current room if not global
        if (currentRoom && currentRoom !== 'global') {
            socket.emit('join_room', currentRoom);
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 flex flex-col items-center p-4">
            <ToastContainer position="top-right" autoClose={3000} />

            {!isUsernameSet ? (
                <form onSubmit={handleUsernameSubmit} className="username-form bg-white p-6 rounded shadow w-full max-w-sm">
                    <h2 className="text-xl font-semibold mb-4">Enter Username</h2>
                    <input
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder="Your username"
                        className="border p-2 w-full rounded mb-4"
                    />
                    <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded w-full">Join Chat</button>
                </form>
            ) : (
                <div className="w-full max-w-2xl space-y-4">
                    <div className="chat-header">
                        <h2 className="text-2xl font-bold text-center">Real-Time Chat</h2>
                        <div className="header-controls">
                            <button
                                className="search-btn"
                                onClick={() => setShowMessageSearch(true)}
                                title="Search Messages"
                            >
                                🔍
                            </button>
                            <button
                                className="notification-settings-btn"
                                onClick={() => setShowNotificationSettings(true)}
                                title="Notification Settings"
                            >
                                🔔
                            </button>
                        </div>
                    </div>

                    <UnreadCounter
                        messages={messages}
                        currentUser={username}
                        currentRoom={currentRoom}
                        recipient={recipient}
                        isWindowFocused={isWindowFocused}
                    />

                    <ConnectionStatus
                        socket={socket}
                        onReconnect={handleReconnection}
                    />

                    <p className="text-center"><strong>Current Room:</strong> {currentRoom}</p>

                    <RoomJoiner
                        newRoom={newRoom}
                        setNewRoom={setNewRoom}
                        setCurrentRoom={setCurrentRoom}
                        socket={socket}
                    />

                    <UserList
                        onlineUsers={onlineUsers}
                        username={username}
                        recipient={recipient}
                        setRecipient={setRecipient}
                    />

                    <div className="chat-container">
                      <MessagePagination
                          socket={socket}
                          messages={messages}
                          onMessagesLoaded={handleOlderMessagesLoaded}
                          enabled={!isSearchActive}
                      />

                      <ChatWindow
                          messages={isSearchActive ? searchResults : messages}
                          typingUser={!isSearchActive ? typingUser : ''}
                          username={username}
                          onAddReaction={handleAddReaction}
                          onMarkMessageRead={handleMarkMessageRead}
                      />

                      {isSearchActive && (
                          <div className="search-active-indicator">
                              <span>Showing {searchResults.length} search results</span>
                              <button
                                  className="clear-search-btn"
                                  onClick={() => setIsSearchActive(false)}
                              >
                                  Show all messages
                              </button>
                          </div>
                      )}

                      <MessageInput
                          message={message}
                          setMessage={setMessage}
                          socket={socket}
                          recipient={recipient}
                          currentRoom={currentRoom}
                      />
                    </div>
                </div>
            )}

            {/* Notification Settings Modal */}
            <NotificationSettings
                isOpen={showNotificationSettings}
                onClose={() => setShowNotificationSettings(false)}
            />

            {/* Message Search Modal */}
            <MessageSearch
                messages={allMessages.length > 0 ? allMessages : messages}
                onSearchResults={handleSearchResults}
                isOpen={showMessageSearch}
                onClose={() => setShowMessageSearch(false)}
            />
        </div>
    );
}

export default App;